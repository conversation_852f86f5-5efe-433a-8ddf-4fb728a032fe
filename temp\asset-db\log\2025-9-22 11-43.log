2025-9-22 11:43:48-debug: start **** info
2025-9-22 11:43:48-log: Cannot access game frame or container.
2025-9-22 11:43:48-debug: asset-db:require-engine-code (442ms)
2025-9-22 11:43:48-log: meshopt wasm decoder initialized
2025-9-22 11:43:48-log: [bullet]:bullet wasm lib loaded.
2025-9-22 11:43:48-log: [box2d]:box2d wasm lib loaded.
2025-9-22 11:43:48-log: Cocos Creator v3.8.6
2025-9-22 11:43:48-log: Forward render pipeline initialized.
2025-9-22 11:43:48-debug: [Assets Memory track]: asset-db:worker-init: initEngine start:30.88MB, end 80.02MB, increase: 49.14MB
2025-9-22 11:43:49-debug: [Assets Memory track]: asset-db-plugin-register: builder start:84.25MB, end 288.94MB, increase: 204.69MB
2025-9-22 11:43:48-debug: [Assets Memory track]: asset-db-plugin-register: programming start:80.94MB, end 84.21MB, increase: 3.27MB
2025-9-22 11:43:48-log: Using legacy pipeline
2025-9-22 11:43:49-debug: [Assets Memory track]: asset-db-plugin-register: project start:80.78MB, end 287.89MB, increase: 207.11MB
2025-9-22 11:43:49-debug: [Assets Memory track]: asset-db:worker-init: initPlugin start:80.04MB, end 287.92MB, increase: 207.88MB
2025-9-22 11:43:49-debug: run package(honor-mini-game) handler(enable) start
2025-9-22 11:43:49-debug: run package(honor-mini-game) handler(enable) success!
2025-9-22 11:43:49-debug: run package(huawei-agc) handler(enable) start
2025-9-22 11:43:49-debug: run package(huawei-quick-game) handler(enable) start
2025-9-22 11:43:49-debug: run package(huawei-quick-game) handler(enable) success!
2025-9-22 11:43:49-debug: run package(huawei-agc) handler(enable) success!
2025-9-22 11:43:49-debug: run package(linux) handler(enable) start
2025-9-22 11:43:49-debug: run package(ios) handler(enable) start
2025-9-22 11:43:49-debug: run package(ios) handler(enable) success!
2025-9-22 11:43:49-debug: run package(linux) handler(enable) success!
2025-9-22 11:43:49-debug: run package(mac) handler(enable) success!
2025-9-22 11:43:49-debug: run package(migu-mini-game) handler(enable) start
2025-9-22 11:43:49-debug: run package(migu-mini-game) handler(enable) success!
2025-9-22 11:43:49-debug: run package(mac) handler(enable) start
2025-9-22 11:43:49-debug: run package(native) handler(enable) start
2025-9-22 11:43:49-debug: run package(native) handler(enable) success!
2025-9-22 11:43:49-debug: run package(oppo-mini-game) handler(enable) start
2025-9-22 11:43:49-debug: run package(oppo-mini-game) handler(enable) success!
2025-9-22 11:43:49-debug: run package(ohos) handler(enable) start
2025-9-22 11:43:49-debug: run package(ohos) handler(enable) success!
2025-9-22 11:43:49-debug: run package(runtime-dev-tools) handler(enable) start
2025-9-22 11:43:49-debug: run package(runtime-dev-tools) handler(enable) success!
2025-9-22 11:43:49-debug: run package(vivo-mini-game) handler(enable) start
2025-9-22 11:43:49-debug: run package(taobao-mini-game) handler(enable) success!
2025-9-22 11:43:49-debug: run package(web-desktop) handler(enable) start
2025-9-22 11:43:49-debug: run package(taobao-mini-game) handler(enable) start
2025-9-22 11:43:49-debug: run package(vivo-mini-game) handler(enable) success!
2025-9-22 11:43:49-debug: run package(web-mobile) handler(enable) success!
2025-9-22 11:43:49-debug: run package(web-desktop) handler(enable) success!
2025-9-22 11:43:49-debug: run package(wechatgame) handler(enable) start
2025-9-22 11:43:49-debug: run package(web-mobile) handler(enable) start
2025-9-22 11:43:49-debug: run package(wechatprogram) handler(enable) start
2025-9-22 11:43:49-debug: run package(wechatprogram) handler(enable) success!
2025-9-22 11:43:49-debug: run package(wechatgame) handler(enable) success!
2025-9-22 11:43:49-debug: run package(windows) handler(enable) start
2025-9-22 11:43:49-debug: run package(windows) handler(enable) success!
2025-9-22 11:43:49-debug: run package(xiaomi-quick-game) handler(enable) start
2025-9-22 11:43:49-debug: run package(cocos-service) handler(enable) start
2025-9-22 11:43:49-debug: run package(xiaomi-quick-game) handler(enable) success!
2025-9-22 11:43:49-debug: run package(cocos-service) handler(enable) success!
2025-9-22 11:43:49-debug: run package(fix-asset-default-userdata) handler(enable) start
2025-9-22 11:43:49-debug: run package(fix-asset-default-userdata) handler(enable) success!
2025-9-22 11:43:49-debug: run package(im-plugin) handler(enable) start
2025-9-22 11:43:49-debug: run package(im-plugin) handler(enable) success!
2025-9-22 11:43:49-debug: start refresh asset from db://assets/editor/enum-gen/EmitterEnum.ts...
2025-9-22 11:43:49-debug: run package(emitter-editor) handler(enable) success!
2025-9-22 11:43:49-debug: start refresh asset from db://assets/editor/enum-gen/EnemyEnum.ts...
2025-9-22 11:43:49-debug: refresh asset db://assets/editor/enum-gen success
2025-9-22 11:43:49-debug: refresh asset db://assets/editor/enum-gen success
2025-9-22 11:43:49-debug: run package(level-editor) handler(enable) success!
2025-9-22 11:43:49-debug: run package(localization-editor) handler(enable) start
2025-9-22 11:43:49-debug: run package(level-editor) handler(enable) start
2025-9-22 11:43:49-debug: run package(emitter-editor) handler(enable) start
2025-9-22 11:43:49-debug: run package(localization-editor) handler(enable) success!
2025-9-22 11:43:49-debug: asset-db:worker-init: initPlugin (1092ms)
2025-9-22 11:43:49-debug: run package(wave-editor) handler(enable) start
2025-9-22 11:43:49-debug: run package(wave-editor) handler(enable) success!
2025-9-22 11:43:49-debug: Run asset db hook programming:beforePreStart ...
2025-9-22 11:43:49-debug: [Assets Memory track]: asset-db:worker-init start:30.88MB, end 296.95MB, increase: 266.08MB
2025-9-22 11:43:49-debug: Run asset db hook engine-extends:beforePreStart success!
2025-9-22 11:43:49-debug: Run asset db hook programming:beforePreStart success!
2025-9-22 11:43:49-debug: Run asset db hook engine-extends:beforePreStart ...
2025-9-22 11:43:49-debug: run package(placeholder) handler(enable) start
2025-9-22 11:43:49-debug: run package(placeholder) handler(enable) success!
2025-9-22 11:43:49-debug: asset-db:worker-init (1677ms)
2025-9-22 11:43:49-debug: asset-db-hook-programming-beforePreStart (52ms)
2025-9-22 11:43:49-debug: asset-db-hook-engine-extends-beforePreStart (52ms)
2025-9-22 11:43:49-debug: Preimport db internal success
2025-9-22 11:43:49-debug: Preimport db assets success
2025-9-22 11:43:49-debug: Run asset db hook programming:afterPreStart ...
2025-9-22 11:43:49-debug: starting packer-driver...
2025-9-22 11:43:56-debug: initialize scripting environment...
2025-9-22 11:43:56-debug: [[Executor]] prepare before lock
2025-9-22 11:43:56-debug: Set detail map pack:///resolution-detail-map.json: {
  "./chunks/79/7983294ee03fd2619ed97c3289fe6adabb609214.js": {
    "__unresolved_2": {
      "error": "Error: 以 file:///E:/M2Game/Client/assets/bundles/common/script/game/data/BossData.ts 为起点找不到模块 \"./EnemyWave\"",
      "messages": [
        {
          "level": "warn",
          "text": "你是否遗漏了扩展名？请注意你不能在模块说明符中省略扩展名。"
        }
      ]
    }
  }
}

2025-9-22 11:43:56-debug: [[Executor]] prepare after unlock
2025-9-22 11:43:56-debug: Run asset db hook programming:afterPreStart success!
2025-9-22 11:43:56-debug: Run asset db hook engine-extends:afterPreStart success!
2025-9-22 11:43:56-debug: Run asset db hook engine-extends:afterPreStart ...
2025-9-22 11:43:56-debug: Start up the 'internal' database...
2025-9-22 11:43:56-debug: asset-db-hook-programming-afterPreStart (6764ms)
2025-9-22 11:43:56-debug: asset-db:worker-effect-data-processing (204ms)
2025-9-22 11:43:56-debug: asset-db-hook-engine-extends-afterPreStart (204ms)
2025-9-22 11:43:56-debug: Start up the 'assets' database...
2025-9-22 11:43:56-debug: asset-db:worker-startup-database[internal] (6986ms)
2025-9-22 11:43:56-debug: [Assets Memory track]: asset-db:worker-init: startup start:176.52MB, end 191.32MB, increase: 14.80MB
2025-9-22 11:43:56-debug: lazy register asset handler *
2025-9-22 11:43:56-debug: lazy register asset handler directory
2025-9-22 11:43:56-debug: lazy register asset handler spine-data
2025-9-22 11:43:56-debug: lazy register asset handler json
2025-9-22 11:43:56-debug: lazy register asset handler dragonbones
2025-9-22 11:43:56-debug: lazy register asset handler dragonbones-atlas
2025-9-22 11:43:56-debug: lazy register asset handler javascript
2025-9-22 11:43:56-debug: lazy register asset handler text
2025-9-22 11:43:56-debug: lazy register asset handler typescript
2025-9-22 11:43:56-debug: lazy register asset handler prefab
2025-9-22 11:43:56-debug: lazy register asset handler scene
2025-9-22 11:43:56-debug: lazy register asset handler terrain
2025-9-22 11:43:56-debug: lazy register asset handler sprite-frame
2025-9-22 11:43:56-debug: lazy register asset handler buffer
2025-9-22 11:43:56-debug: lazy register asset handler tiled-map
2025-9-22 11:43:56-debug: lazy register asset handler alpha-image
2025-9-22 11:43:56-debug: lazy register asset handler sign-image
2025-9-22 11:43:56-debug: lazy register asset handler image
2025-9-22 11:43:56-debug: lazy register asset handler texture
2025-9-22 11:43:56-debug: lazy register asset handler render-texture
2025-9-22 11:43:56-debug: lazy register asset handler erp-texture-cube
2025-9-22 11:43:56-debug: lazy register asset handler rt-sprite-frame
2025-9-22 11:43:56-debug: lazy register asset handler texture-cube
2025-9-22 11:43:56-debug: lazy register asset handler gltf-mesh
2025-9-22 11:43:56-debug: lazy register asset handler texture-cube-face
2025-9-22 11:43:56-debug: lazy register asset handler gltf-animation
2025-9-22 11:43:56-debug: lazy register asset handler gltf-skeleton
2025-9-22 11:43:56-debug: lazy register asset handler gltf-material
2025-9-22 11:43:56-debug: lazy register asset handler gltf
2025-9-22 11:43:56-debug: lazy register asset handler gltf-scene
2025-9-22 11:43:56-debug: lazy register asset handler gltf-embeded-image
2025-9-22 11:43:56-debug: lazy register asset handler physics-material
2025-9-22 11:43:56-debug: lazy register asset handler effect
2025-9-22 11:43:56-debug: lazy register asset handler material
2025-9-22 11:43:56-debug: lazy register asset handler effect-header
2025-9-22 11:43:56-debug: lazy register asset handler fbx
2025-9-22 11:43:56-debug: lazy register asset handler animation-clip
2025-9-22 11:43:56-debug: lazy register asset handler animation-graph
2025-9-22 11:43:56-debug: lazy register asset handler animation-graph-variant
2025-9-22 11:43:56-debug: lazy register asset handler audio-clip
2025-9-22 11:43:56-debug: lazy register asset handler ttf-font
2025-9-22 11:43:56-debug: lazy register asset handler animation-mask
2025-9-22 11:43:56-debug: lazy register asset handler sprite-atlas
2025-9-22 11:43:56-debug: lazy register asset handler particle
2025-9-22 11:43:56-debug: lazy register asset handler auto-atlas
2025-9-22 11:43:56-debug: lazy register asset handler bitmap-font
2025-9-22 11:43:56-debug: lazy register asset handler render-pipeline
2025-9-22 11:43:56-debug: lazy register asset handler label-atlas
2025-9-22 11:43:56-debug: lazy register asset handler render-stage
2025-9-22 11:43:56-debug: lazy register asset handler instantiation-material
2025-9-22 11:43:56-debug: lazy register asset handler instantiation-mesh
2025-9-22 11:43:56-debug: lazy register asset handler render-flow
2025-9-22 11:43:56-debug: lazy register asset handler instantiation-skeleton
2025-9-22 11:43:56-debug: lazy register asset handler instantiation-animation
2025-9-22 11:43:56-debug: lazy register asset handler video-clip
2025-9-22 11:43:56-debug: asset-db:worker-startup-database[assets] (6983ms)
2025-9-22 11:43:56-debug: asset-db:start-database (7060ms)
2025-9-22 11:43:56-debug: asset-db:ready (10192ms)
2025-9-22 11:43:56-debug: init worker message success
2025-9-22 11:43:56-debug: fix the bug of updateDefaultUserData
2025-9-22 11:43:56-debug: programming:execute-script (5ms)
2025-9-22 11:43:57-debug: [Build Memory track]: builder:worker-init start:195.96MB, end 208.34MB, increase: 12.39MB
2025-9-22 11:43:57-debug: builder:worker-init (272ms)
2025-9-22 11:44:02-debug: refresh db internal success
2025-9-22 11:44:02-debug: refresh db assets success
2025-9-22 11:44:02-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-22 11:44:02-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-22 11:44:02-debug: asset-db:refresh-all-database (122ms)
2025-9-22 11:44:02-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-22 11:44:02-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-22 11:45:34-debug: refresh db internal success
2025-9-22 11:45:34-debug: refresh db assets success
2025-9-22 11:45:34-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-22 11:45:34-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-22 11:45:34-debug: asset-db:refresh-all-database (150ms)
2025-9-22 11:47:58-debug: refresh db internal success
2025-9-22 11:47:58-debug: refresh db assets success
2025-9-22 11:47:58-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-22 11:47:58-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-22 11:47:58-debug: asset-db:refresh-all-database (158ms)
2025-9-22 11:47:58-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-22 11:47:58-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-22 11:54:41-debug: refresh db internal success
2025-9-22 11:54:41-debug: refresh db assets success
2025-9-22 11:54:41-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-22 11:54:41-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-22 11:54:41-debug: asset-db:refresh-all-database (155ms)
2025-9-22 11:54:41-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-22 11:54:41-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-22 11:56:14-debug: refresh db internal success
2025-9-22 11:56:14-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\wave\Wave.ts
background: #aaff85; color: #000;
color: #000;
2025-9-22 11:56:14-debug: refresh db assets success
2025-9-22 11:56:14-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-22 11:56:14-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-22 11:56:14-debug: asset-db:refresh-all-database (135ms)
2025-9-22 11:57:53-debug: refresh db internal success
2025-9-22 11:57:53-debug: refresh db assets success
2025-9-22 11:57:53-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-22 11:57:53-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-22 11:57:53-debug: asset-db:refresh-all-database (143ms)
2025-9-22 11:57:53-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-22 11:57:53-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-22 11:58:00-debug: %cImport%c: E:\M2Game\Client\assets\scenes\BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-22 11:58:00-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (4ms)
