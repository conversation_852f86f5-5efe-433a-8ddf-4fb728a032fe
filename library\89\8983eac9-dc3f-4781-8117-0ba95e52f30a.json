[{"__type__": "cc.Prefab", "_name": "Bullet_211200103", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "persistent": false}, {"__type__": "cc.Node", "_name": "Bullet_211200103", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [], "_active": true, "_components": [{"__id__": 2}, {"__id__": 4}], "_prefab": {"__id__": 27}, "_lpos": {"__type__": "cc.Vec3", "x": 59.526, "y": 585.418, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 3}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "begKFdA65LzYkynT0HbkIl"}, {"__type__": "2564dArcRFKZKoo3odCQrHw", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 5}, "bulletID": 0, "bulletPrefab": null, "emitterData": {"__id__": 6}, "bulletData": {"__id__": 20}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "31k/54u8dPRZsJ/5MHRwps"}, {"__type__": "EmitterData", "isOnlyInScreen": true, "initialDelay": {"__id__": 7}, "emitDuration": {"__id__": 8}, "isPreWarm": false, "preWarmDuration": {"__id__": 9}, "preWarmEffect": null, "isLoop": true, "loopInterval": {"__id__": 10}, "emitInterval": {"__id__": 11}, "emitPower": {"__id__": 12}, "perEmitCount": {"__id__": 13}, "perEmitInterval": {"__id__": 14}, "perEmitOffsetX": {"__id__": 15}, "angle": {"__id__": 16}, "count": {"__id__": 17}, "arc": {"__id__": 18}, "radius": {"__id__": 19}, "emitEffect": null, "eventGroupData": []}, {"__type__": "ExpressionValue", "value": 1000, "isExpression": false, "expression": "1000", "serializedProgram": null}, {"__type__": "ExpressionValue", "value": 30000, "isExpression": false, "expression": "30000", "serializedProgram": null}, {"__type__": "ExpressionValue", "value": 0, "isExpression": false, "expression": "0", "serializedProgram": null}, {"__type__": "ExpressionValue", "value": 0, "isExpression": false, "expression": "0", "serializedProgram": null}, {"__type__": "ExpressionValue", "value": 3000, "isExpression": false, "expression": "3000", "serializedProgram": null}, {"__type__": "ExpressionValue", "value": 1, "isExpression": false, "expression": "1", "serializedProgram": null}, {"__type__": "ExpressionValue", "value": 1, "isExpression": false, "expression": "1", "serializedProgram": null}, {"__type__": "ExpressionValue", "value": 0, "isExpression": false, "expression": "0", "serializedProgram": null}, {"__type__": "ExpressionValue", "value": 0, "isExpression": false, "expression": "0", "serializedProgram": null}, {"__type__": "ExpressionValue", "value": 270, "isExpression": false, "expression": "270", "serializedProgram": null}, {"__type__": "ExpressionValue", "value": 1, "isExpression": false, "expression": "1", "serializedProgram": null}, {"__type__": "ExpressionValue", "value": 60, "isExpression": false, "expression": "60", "serializedProgram": null}, {"__type__": "ExpressionValue", "value": 1, "isExpression": false, "expression": "1", "serializedProgram": null}, {"__type__": "BulletData", "isFacingMoveDir": true, "isTrackingTarget": false, "isDestroyOutScreen": true, "isDestructive": false, "isDestructiveOnHit": false, "scale": {"__id__": 21}, "duration": {"__id__": 22}, "delayDestroy": {"__id__": 23}, "speed": {"__id__": 24}, "acceleration": {"__id__": 25}, "accelerationAngle": {"__id__": 26}, "eventGroupData": []}, {"__type__": "ExpressionValue", "value": 1, "isExpression": false, "expression": "1", "serializedProgram": null}, {"__type__": "ExpressionValue", "value": 5000, "isExpression": false, "expression": "5000", "serializedProgram": null}, {"__type__": "ExpressionValue", "value": 0, "isExpression": false, "expression": "0", "serializedProgram": null}, {"__type__": "ExpressionValue", "value": 900, "isExpression": false, "expression": "900", "serializedProgram": null}, {"__type__": "ExpressionValue", "value": 0, "isExpression": false, "expression": "0", "serializedProgram": null}, {"__type__": "ExpressionValue", "value": 0, "isExpression": false, "expression": "0", "serializedProgram": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f6gcZ/lXZAt4gDq6zMfn2j", "targetOverrides": null}]