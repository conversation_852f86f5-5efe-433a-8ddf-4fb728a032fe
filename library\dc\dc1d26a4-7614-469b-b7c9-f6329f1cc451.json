[{"__type__": "cc.Prefab", "_name": "RandomElement_StartCloud", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "persistent": false}, {"__type__": "cc.Node", "_name": "RandomElement_StartCloud", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 5}], "_active": true, "_components": [{"__id__": 8}, {"__id__": 10}], "_prefab": {"__id__": 14}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 1}, "_prefab": {"__id__": 3}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 2}, "asset": {"__uuid__": "1f86a690-2793-4143-a94d-c4aa0099b0da", "__expectedType__": "cc.Prefab"}, "fileId": "ebE3ClO65FEavwhVAL65ht", "instance": {"__id__": 4}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "caZH6bCFZOt4sapq9gq0Mh", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 1}, "_prefab": {"__id__": 6}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 5}, "asset": {"__uuid__": "b70143d5-a8b5-4c0f-8f7a-4c320af9b7d3", "__expectedType__": "cc.Prefab"}, "fileId": "87wMLDdGJGwoHG0lk0VpLH", "instance": {"__id__": 7}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "e6+kdQP85HGY/KQggr/2aG", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 9}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "48ODoXnJ9J3b58WDD6RtLO"}, {"__type__": "c06femD0WhPk63lQ/D1YJtE", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 11}, "terrain": [{"__id__": 12}, {"__id__": 13}], "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4d/a+smUtPeZ18iQ2ULM15"}, {"__type__": "TerrainElem", "weight": 100, "elem": {"__uuid__": "1f86a690-2793-4143-a94d-c4aa0099b0da", "__expectedType__": "cc.Prefab"}, "offSet": {"__type__": "cc.Vec2", "x": 0, "y": 0}}, {"__type__": "TerrainElem", "weight": 100, "elem": {"__uuid__": "b70143d5-a8b5-4c0f-8f7a-4c320af9b7d3", "__expectedType__": "cc.Prefab"}, "offSet": {"__type__": "cc.Vec2", "x": 0, "y": 0}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "56xTtHP4tJg4s0D/yyljUz", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": [{"__id__": 5}, {"__id__": 2}]}]