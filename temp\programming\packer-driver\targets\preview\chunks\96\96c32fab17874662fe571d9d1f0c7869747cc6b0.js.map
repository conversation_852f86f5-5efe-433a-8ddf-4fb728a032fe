{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/dyncTerrain/EmittierTerrain.ts"], "names": ["EmittierElem", "_decorator", "assetManager", "Camera", "CCBoolean", "CCFloat", "CCInteger", "Component", "director", "Enum", "instantiate", "Prefab", "Vec3", "view", "EDITOR", "LayerEmittierStrategy", "LayerEmittierType", "LayerRandomRange", "GameIns", "ccclass", "property", "executeInEditMode", "LayerEmittierTypeZh", "Infinite", "Duration", "Count", "Event", "EmittierStatus", "SerializableRandomRange", "type", "displayName", "toLayerRandomRange", "min", "max", "fromLayerRandomRange", "range", "_velocity", "init", "speed", "angle", "rad", "Math", "PI", "x", "cos", "y", "sin", "tick", "dt", "pos", "node", "position", "clone", "add", "multiplyScalar", "setPosition", "_checkOutOfBounds", "mainCamera", "_findSceneCamera", "worldPos", "worldPosition", "screenPos", "worldToScreen", "screenSize", "getVisibleSize", "buffer", "width", "height", "destroy", "scene", "getScene", "findCameraRecursive", "camera", "getComponent", "child", "children", "foundCamera", "rootNode", "EmittierTerrain", "visible", "tooltip", "_status", "inactive", "_activeElements", "_curDelay", "_curValue", "_curEmiIndex", "_deltaTime", "_lastEmitTime", "_emitCount", "_nextInterval", "_initialDelayPassed", "bfollow", "value", "_bfollow", "typeZh", "onLoad", "_resetData", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "loadAny", "uuid", "emittier", "err", "prefab", "emitterNode", "<PERSON><PERSON><PERSON><PERSON>", "startEmittier", "active", "initDelay", "delayModify", "random", "valueModify", "battleManager", "dtMs", "_updateEmitter", "_updateActiveElements", "delaySeconds", "_<PERSON><PERSON><PERSON><PERSON>", "i", "length", "elem", "<PERSON><PERSON><PERSON><PERSON>", "splice", "emittierElements", "currentTime", "strategy", "Random", "floor", "Sequence", "elemPrefab", "elemNode", "offsetX", "offSetX", "console", "log", "angleModify", "speedModify", "elemComponent", "addComponent", "push", "interval", "intervalModify", "_checkEmittierEnd", "end", "_destroyEmitter", "_destroyAllElements", "play", "bPlay", "onDestroy"], "mappings": ";;;yRAuCaA,Y;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAvCJC,MAAAA,U,OAAAA,U;AAAYC,MAAAA,Y,OAAAA,Y;AAAcC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,O,OAAAA,O;AAASC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,Q,OAAAA,Q;AAAUC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,I,OAAAA,I;;AACvHC,MAAAA,M,UAAAA,M;;AACAC,MAAAA,qB,iBAAAA,qB;AAAuBC,MAAAA,iB,iBAAAA,iB;AAAmBC,MAAAA,gB,iBAAAA,gB;;AAC1CC,MAAAA,O,iBAAAA,O;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,QAAX;AAAqBC,QAAAA;AAArB,O,GAA2CpB,U;;AAE5CqB,MAAAA,mB,aAAAA,mB;AAAAA,QAAAA,mB,CAAAA,mB,mBACI;AAAA;AAAA,oDAAkBC,Q;AADtBD,QAAAA,mB,CAAAA,mB,+BAEM;AAAA;AAAA,oDAAkBE,Q;AAFxBF,QAAAA,mB,CAAAA,mB,+BAGM;AAAA;AAAA,oDAAkBG,K;AAHxBH,QAAAA,mB,CAAAA,mB,+BAIM;AAAA;AAAA,oDAAkBI,K;eAJxBJ,mB;QAAAA,mB;;AAOAK,MAAAA,c,0BAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;eAAAA,c;QAAAA,c;;yCAQQC,uB,WADZT,OAAO,CAAC,yBAAD,C,UAEHC,QAAQ,CAAC;AAACS,QAAAA,IAAI,EAAExB,OAAP;AAAgByB,QAAAA,WAAW,EAAE;AAA7B,OAAD,C,UAGRV,QAAQ,CAAC;AAACS,QAAAA,IAAI,EAAExB,OAAP;AAAgByB,QAAAA,WAAW,EAAE;AAA7B,OAAD,C,2BALb,MACaF,uBADb,CACqC;AAAA;AAAA;;AAAA;AAAA;;AAOjC;AACAG,QAAAA,kBAAkB,GAAqB;AACnC,iBAAO;AAAA;AAAA,oDAAqB,KAAKC,GAA1B,EAA+B,KAAKC,GAApC,CAAP;AACH;;AAEDC,QAAAA,oBAAoB,CAACC,KAAD,EAAgC;AAChD,eAAKH,GAAL,GAAWG,KAAK,CAACH,GAAjB;AACA,eAAKC,GAAL,GAAWE,KAAK,CAACF,GAAjB;AACH;;AAfgC,O;;;;;iBAEZ,C;;;;;;;iBAGA,C;;;;8BAaZjC,Y,GAAN,MAAMA,YAAN,SAA2BO,SAA3B,CAAqC;AAAA;AAAA;AAAA,eAChC6B,SADgC,GACd,IAAIxB,IAAJ,CAAS,CAAT,EAAY,CAAZ,EAAe,CAAf,CADc;AAAA;;AAGxC;AACJ;AACA;AACA;AACA;AACWyB,QAAAA,IAAI,CAACC,KAAD,EAAgBC,KAAhB,EAAqC;AAE5C;AACA,cAAMC,GAAG,GAAGD,KAAK,GAAGE,IAAI,CAACC,EAAb,GAAkB,GAA9B,CAH4C,CAK5C;;AACA,eAAKN,SAAL,CAAeO,CAAf,GAAmBF,IAAI,CAACG,GAAL,CAASJ,GAAT,IAAgBF,KAAnC;AACA,eAAKF,SAAL,CAAeS,CAAf,GAAmBJ,IAAI,CAACK,GAAL,CAASN,GAAT,IAAgBF,KAAnC;AACH;;AAEDS,QAAAA,IAAI,CAACC,EAAD,EAAmB;AACnB;AACA,cAAMC,GAAG,GAAG,KAAKC,IAAL,CAAUC,QAAV,CAAmBC,KAAnB,EAAZ;AACAH,UAAAA,GAAG,CAACI,GAAJ,CAAQ,KAAKjB,SAAL,CAAegB,KAAf,GAAuBE,cAAvB,CAAsCN,EAAtC,CAAR;AACA,eAAKE,IAAL,CAAUK,WAAV,CAAsBN,GAAtB,EAJmB,CAMnB;;AACA,eAAKO,iBAAL;AACH;;AAEOA,QAAAA,iBAAiB,GAAS;AAC9B,cAAMC,UAAU,GAAG,KAAKC,gBAAL,EAAnB;;AACA,cAAI,CAACD,UAAL,EAAiB,OAFa,CAI9B;;AACA,cAAME,QAAQ,GAAG,KAAKT,IAAL,CAAUU,aAA3B,CAL8B,CAO9B;;AACA,cAAMC,SAAS,GAAG,IAAIjD,IAAJ,EAAlB;AACA6C,UAAAA,UAAU,CAACK,aAAX,CAAyBD,SAAzB,EAAoCF,QAApC,EAT8B,CAW9B;;AACA,cAAMI,UAAU,GAAGlD,IAAI,CAACmD,cAAL,EAAnB,CAZ8B,CAc9B;;AACA,cAAMC,MAAM,GAAG,EAAf,CAf8B,CAiB9B;;AACA,cACIJ,SAAS,CAAClB,CAAV,GAAc,CAACsB,MAAf,IACAJ,SAAS,CAAClB,CAAV,GAAcoB,UAAU,CAACG,KAAX,GAAmBD,MADjC,IAEAJ,SAAS,CAAChB,CAAV,GAAc,CAACoB,MAFf,IAGAJ,SAAS,CAAChB,CAAV,GAAckB,UAAU,CAACI,MAAX,GAAoBF,MAJtC,EAKE;AACE;AACA,iBAAKf,IAAL,CAAUkB,OAAV;AACH;AACJ;;AAEOV,QAAAA,gBAAgB,GAAkB;AAClC,cAAMW,KAAK,GAAG7D,QAAQ,CAAC8D,QAAT,EAAd;AACA,cAAI,CAACD,KAAL,EAAY,OAAO,IAAP,CAFsB,CAIlC;;AACA,cAAME,mBAAmB,GAAIrB,IAAD,IAA8B;AACtD,gBAAMsB,MAAM,GAAGtB,IAAI,CAACuB,YAAL,CAAkBtE,MAAlB,CAAf;AACA,gBAAIqE,MAAJ,EAAY,OAAOA,MAAP;;AAEZ,iBAAK,IAAME,KAAX,IAAoBxB,IAAI,CAACyB,QAAzB,EAAmC;AAC/B,kBAAMC,WAAW,GAAGL,mBAAmB,CAACG,KAAD,CAAvC;AACA,kBAAIE,WAAJ,EAAiB,OAAOA,WAAP;AACpB;;AACD,mBAAO,IAAP;AACH,WATD;;AAWA,eAAK,IAAMC,QAAX,IAAuBR,KAAK,CAACM,QAA7B,EAAuC;AACnC,gBAAMH,MAAM,GAAGD,mBAAmB,CAACM,QAAD,CAAlC;AACA,gBAAIL,MAAJ,EAAY,OAAOA,MAAP;AACf;;AAED,iBAAO,IAAP;AACH;;AA/EmC,O;;iCAoF/BM,e,YAFZ3D,OAAO,CAAC,iBAAD,C,UACPE,iBAAiB,E,UAGbD,QAAQ,CAAC;AAAC2D,QAAAA,OAAO,EAAE;AAAV,OAAD,C,UAER3D,QAAQ,CAAC;AAACS,QAAAA,IAAI,EAAEzB,SAAP;AAAkB0B,QAAAA,WAAW,EAAE;AAA/B,OAAD,C,UAGRV,QAAQ,CAAC;AAACS,QAAAA,IAAI,EAAElB,MAAP;AAAemB,QAAAA,WAAW,EAAE;AAA5B,OAAD,C,UAERV,QAAQ,CAAC;AAACS,QAAAA,IAAI,EAAEpB,IAAI;AAAA;AAAA,2DAAX;AAAoCqB,QAAAA,WAAW,EAAE;AAAjD,OAAD,C,WAERV,QAAQ,CAAC;AAACS,QAAAA,IAAI,EAAE,CAAClB,MAAD,CAAP;AAAiBmB,QAAAA,WAAW,EAAE;AAA9B,OAAD,C,WAERV,QAAQ,CAAC;AAACS,QAAAA,IAAI,EAAEpB,IAAI,CAACa,mBAAD,CAAX;AAAkCQ,QAAAA,WAAW,EAAC;AAA9C,OAAD,C,WAGRV,QAAQ,CAAC;AAAC2D,QAAAA,OAAO,EAAE;AAAV,OAAD,C,WAER3D,QAAQ,CAAC;AAACS,QAAAA,IAAI,EAAExB,OAAP;AAAgByB,QAAAA,WAAW,EAAE,KAA7B;AAAoCkD,QAAAA,OAAO,EAAE;AAA7C,OAAD,C,WAER5D,QAAQ,CAAC;AAACS,QAAAA,IAAI,EAAED,uBAAP;AAAgCE,QAAAA,WAAW,EAAE;AAA7C,OAAD,C,WAERV,QAAQ,CAAC;AAACS,QAAAA,IAAI,EAAEvB,SAAP;AAAkBwB,QAAAA,WAAW,EAAE,UAA/B;AAA2CE,QAAAA,GAAG,EAAE;AAAhD,OAAD,C,WAERZ,QAAQ,CAAC;AAACS,QAAAA,IAAI,EAAED,uBAAP;AAAgCE,QAAAA,WAAW,EAAE;AAA7C,OAAD,C,WAERV,QAAQ,CAAC;AAACS,QAAAA,IAAI,EAAEvB,SAAP;AAAkBwB,QAAAA,WAAW,EAAE;AAA/B,OAAD,C,WAERV,QAAQ,CAAC;AAACS,QAAAA,IAAI,EAAED,uBAAP;AAAgCE,QAAAA,WAAW,EAAE;AAA7C,OAAD,C,WAERV,QAAQ,CAAC;AAACS,QAAAA,IAAI,EAAEvB,SAAP;AAAkBwB,QAAAA,WAAW,EAAE,aAA/B;AAA6CE,QAAAA,GAAG,EAAE,CAAlD;AAAqDC,QAAAA,GAAG,EAAE;AAA1D,OAAD,C,WAERb,QAAQ,CAAC;AAACS,QAAAA,IAAI,EAAED,uBAAP;AAAgCE,QAAAA,WAAW,EAAE;AAA7C,OAAD,C,WAERV,QAAQ,CAAC;AAACS,QAAAA,IAAI,EAAExB,OAAP;AAAgByB,QAAAA,WAAW,EAAE;AAA7B,OAAD,C,WAERV,QAAQ,CAAC;AAACS,QAAAA,IAAI,EAAED,uBAAP;AAAgCE,QAAAA,WAAW,EAAE;AAA7C,OAAD,C,WAERV,QAAQ,CAAC;AAACS,QAAAA,IAAI,EAAED,uBAAP;AAAgCE,QAAAA,WAAW,EAAC;AAA5C,OAAD,C,6CAxCb,MAEagD,eAFb,SAEqCvE,SAFrC,CAE+C;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAmBjB;AAnBiB;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,eAyCnC0E,OAzCmC,GAyCTtD,cAAc,CAACuD,QAzCN;AAAA,eA0CnCC,eA1CmC,GA0CD,EA1CC;AAAA,eA4CnCC,SA5CmC,GA4Cf,CA5Ce;AA4CZ;AA5CY,eA6CnCC,SA7CmC,GA6Cf,CA7Ce;AA6CZ;AA7CY,eA8CnCC,YA9CmC,GA8CZ,CA9CY;AA8CT;AA9CS,eAgDnCC,UAhDmC,GAgDtB,CAhDsB;AAgDnB;AAhDmB,eAiDnCC,aAjDmC,GAiDnB,CAjDmB;AAiDhB;AAjDgB,eAkDnCC,UAlDmC,GAkDd,CAlDc;AAkDX;AAlDW,eAmDnCC,aAnDmC,GAmDX,CAnDW;AAmDR;AAnDQ,eAoDnCC,mBApDmC,GAoDJ,KApDI;AAAA;;AAKzB,YAAPC,OAAO,CAACC,KAAD,EAAiB;AAAE,eAAKC,QAAL,GAAgBD,KAAhB;AAAwB;;AAC3C,YAAPD,OAAO,GAAY;AAAE,iBAAO,KAAKE,QAAZ;AAAuB;;AAQtC,YAANC,MAAM,GAAwB;AAAE,iBAAO,KAAKlE,IAAZ;AAAoD;;AAC9E,YAANkE,MAAM,CAACF,KAAD,EAA6B;AAAE,eAAKhE,IAAL,GAAYgE,KAAZ;AAAmD;;AAqCrD;AAEpCG,QAAAA,MAAM,GAAS;AACrB,eAAKC,UAAL;;AAEA,cAAInF,MAAJ,EAAY;AACR,iBAAKoC,IAAL,CAAUgD,iBAAV;AACAhG,YAAAA,YAAY,CAACiG,OAAb,CAAqB;AAAEC,cAAAA,IAAI,EAAE,KAAKC,QAAL,CAAeD;AAAvB,aAArB,EAAoD,CAACE,GAAD,EAAMC,MAAN,KAAyB;AACzE,kBAAID,GAAJ,EAAS;AACL;AACH,eAFD,MAEO;AAEH,oBAAME,WAAW,GAAG9F,WAAW,CAAC6F,MAAD,CAA/B;AACA,qBAAKrD,IAAL,CAAUuD,QAAV,CAAmBD,WAAnB;AACH;AACJ,aARD;AASH;AACJ;;AAEME,QAAAA,aAAa,GAAS;AACzB,eAAKzB,OAAL,GAAetD,cAAc,CAACgF,MAA9B;;AACA,cAAI7F,MAAJ,EAAY;AACR,iBAAKsE,SAAL,GAAiB,KAAKwB,SAAL,GAAiB,KAAKC,WAAL,CAAkB7E,GAAnC,GAAyCS,IAAI,CAACqE,MAAL,MAAiB,KAAKD,WAAL,CAAkB5E,GAAlB,GAAwB,KAAK4E,WAAL,CAAkB7E,GAA3D,CAA1D;AACA,iBAAKqD,SAAL,GAAiB,KAAKQ,KAAL,GAAapD,IAAI,CAACqE,MAAL,MAAiB,KAAKC,WAAL,CAAkB9E,GAAlB,GAAwB,KAAK8E,WAAL,CAAkB/E,GAA3D,CAA9B;AACH,WAHD,MAGO;AACH,iBAAKoD,SAAL,GAAiB,KAAKwB,SAAL,GAAiB,KAAKC,WAAL,CAAkB7E,GAAnC,GAAyC;AAAA;AAAA,oCAAQgF,aAAR,CAAsBF,MAAtB,MAAkC,KAAKD,WAAL,CAAkB5E,GAAlB,GAAwB,KAAK4E,WAAL,CAAkB7E,GAA5E,CAA1D;AACA,iBAAKqD,SAAL,GAAiB,KAAKQ,KAAL,GAAa,KAAKkB,WAAL,CAAkB/E,GAA/B,GAAqC;AAAA;AAAA,oCAAQgF,aAAR,CAAsBF,MAAtB,MAAkC,KAAKC,WAAL,CAAkB9E,GAAlB,GAAwB,KAAK8E,WAAL,CAAkB/E,GAA5E,CAAtD;AACH;AACJ;;AAEMe,QAAAA,IAAI,CAACC,EAAD,EAAmB;AAC1B,cAAI,KAAKiC,OAAL,KAAiBtD,cAAc,CAACgF,MAApC,EAA4C;AAE5C,cAAMM,IAAI,GAAGjE,EAAE,GAAG,IAAlB,CAH0B,CAI1B;;AACA,eAAKkE,cAAL,CAAoBD,IAApB;;AACA,eAAKE,qBAAL,CAA2BnE,EAA3B;AACH;AAED;AACJ;AACA;AACA;;;AACYkE,QAAAA,cAAc,CAAClE,EAAD,EAAmB;AACrC,eAAKuC,UAAL,IAAmBvC,EAAnB;;AAEA,cAAI,CAAC,KAAK2C,mBAAV,EAA+B;AAC3B,gBAAMyB,YAAY,GAAG,KAAKhC,SAAL,GAAiB,IAAtC;;AACA,gBAAI,KAAKG,UAAL,IAAmB6B,YAAvB,EAAqC;AACjC,mBAAKzB,mBAAL,GAA2B,IAA3B;AACA,mBAAKJ,UAAL,GAAkB,CAAlB,CAFiC,CAEZ;AACxB;;AACD;AACH,WAVoC,CAYrC;;;AACA,eAAK8B,YAAL;AACH;AAED;AACJ;AACA;AACA;;;AACYF,QAAAA,qBAAqB,CAACnE,EAAD,EAAmB;AAC5C;AACA,eAAK,IAAIsE,CAAC,GAAG,KAAKnC,eAAL,CAAqBoC,MAArB,GAA8B,CAA3C,EAA8CD,CAAC,IAAI,CAAnD,EAAsDA,CAAC,EAAvD,EAA2D;AACvD,gBAAME,IAAI,GAAG,KAAKrC,eAAL,CAAqBmC,CAArB,CAAb,CADuD,CAGvD;;AACA,gBAAI,CAACE,IAAI,CAACC,OAAV,EAAmB;AACf;AACA,mBAAKtC,eAAL,CAAqBuC,MAArB,CAA4BJ,CAA5B,EAA+B,CAA/B;;AACA;AACH,aARsD,CAUvD;;;AACAE,YAAAA,IAAI,CAACzE,IAAL,CAAUC,EAAV;AACH;AACJ;;AAEOqE,QAAAA,YAAY,GACpB;AACI,cAAI,CAAC,KAAKhB,QAAN,IAAkB,KAAKsB,gBAAL,CAAsBJ,MAAtB,KAAiC,CAAvD,EAA0D;AAE1D,cAAMK,WAAW,GAAG,KAAKrC,UAAzB,CAHJ,CAKI;;AACA,cAAIqC,WAAW,GAAG,KAAKpC,aAAnB,GAAmC,KAAKE,aAA5C,EAA2D,OAN/D,CAQI;;AACA,cAAI,KAAKmC,QAAL,KAAkB;AAAA;AAAA,8DAAsBC,MAA5C,EAAoD;AAChD,gBAAIhH,MAAJ,EAAY;AACR,mBAAKwE,YAAL,GAAoB7C,IAAI,CAACsF,KAAL,CAAWtF,IAAI,CAACqE,MAAL,KAAgB,KAAKa,gBAAL,CAAsBJ,MAAjD,CAApB;AACH,aAFD,MAEO;AACH,mBAAKjC,YAAL,GAAoB7C,IAAI,CAACsF,KAAL,CAAW;AAAA;AAAA,sCAAQf,aAAR,CAAsBF,MAAtB,KAAiC,KAAKa,gBAAL,CAAsBJ,MAAlE,CAApB;AACH;AACJ,WAND,MAMO,IAAI,KAAKM,QAAL,KAAkB;AAAA;AAAA,8DAAsBG,QAA5C,EAAsD;AACzD,iBAAK1C,YAAL,GAAoB,KAAKG,UAAL,GAAkB,KAAKkC,gBAAL,CAAsBJ,MAA5D;AACH;;AACD,cAAMU,UAAU,GAAG,KAAKN,gBAAL,CAAsB,KAAKrC,YAA3B,CAAnB,CAlBJ,CAoBI;;AACA,cAAM4C,QAAQ,GAAGxH,WAAW,CAACuH,UAAD,CAA5B,CArBJ,CAuBI;;AACA,cAAIE,OAAO,GAAG,CAAd;;AACA,cAAIrH,MAAJ,EAAY;AACRqH,YAAAA,OAAO,GAAG,KAAKC,OAAL,CAAcpG,GAAd,GAAoBS,IAAI,CAACqE,MAAL,MAAiB,KAAKsB,OAAL,CAAcnG,GAAd,GAAoB,KAAKmG,OAAL,CAAcpG,GAAnD,CAA9B;AACAqG,YAAAA,OAAO,CAACC,GAAR,CAAY,aAAZ,EAA2BH,OAA3B;AACH,WAHD,MAGO;AACHA,YAAAA,OAAO,GAAG;AAAA;AAAA,oCAAQnB,aAAR,CAAsBF,MAAtB,MAAkC,KAAKsB,OAAL,CAAcnG,GAAd,GAAoB,KAAKmG,OAAL,CAAcpG,GAApE,CAAV;AACH;;AACDkG,UAAAA,QAAQ,CAAC3E,WAAT,CAAqB,KAAKL,IAAL,CAAUC,QAAV,CAAmBR,CAAnB,GAAuBwF,OAA5C,EAAqD,CAArD,EAAwD,CAAxD,EA/BJ,CAiCI;;AACA,cAAI5F,KAAK,GAAG,CAAZ;;AACA,cAAIzB,MAAJ,EAAY;AACRyB,YAAAA,KAAK,GAAG,KAAKA,KAAL,GAAa,KAAKgG,WAAL,CAAkBvG,GAA/B,GAAqCS,IAAI,CAACqE,MAAL,MAAiB,KAAKyB,WAAL,CAAkBtG,GAAlB,GAAwB,KAAKsG,WAAL,CAAkBvG,GAA3D,CAA7C;AACH,WAFD,MAEO;AACHO,YAAAA,KAAK,GAAG,KAAKA,KAAL,GAAa,KAAKgG,WAAL,CAAkBvG,GAA/B,GAAqC;AAAA;AAAA,oCAAQgF,aAAR,CAAsBF,MAAtB,MAAkC,KAAKyB,WAAL,CAAkBtG,GAAlB,GAAwB,KAAKsG,WAAL,CAAkBvG,GAA5E,CAA7C;AACH,WAvCL,CAyCI;;;AACA,cAAIM,KAAK,GAAG,CAAZ;;AACA,cAAIxB,MAAJ,EAAY;AACRwB,YAAAA,KAAK,GAAG,KAAKA,KAAL,GAAa,KAAKkG,WAAL,CAAkBxG,GAA/B,GAAqCS,IAAI,CAACqE,MAAL,MAAiB,KAAK0B,WAAL,CAAkBvG,GAAlB,GAAwB,KAAKuG,WAAL,CAAkBxG,GAA3D,CAA7C;AACAqG,YAAAA,OAAO,CAACC,GAAR,CAAY,YAAZ,EAA0BhG,KAA1B;AACH,WAHD,MAGO;AACHA,YAAAA,KAAK,GAAG,KAAKA,KAAL,GAAa,KAAKkG,WAAL,CAAkBxG,GAA/B,GAAqC;AAAA;AAAA,oCAAQgF,aAAR,CAAsBF,MAAtB,MAAkC,KAAK0B,WAAL,CAAkBvG,GAAlB,GAAwB,KAAKuG,WAAL,CAAkBxG,GAA5E,CAA7C;AACH,WAhDL,CAkDI;;;AACA,cAAIyG,aAAa,GAAGP,QAAQ,CAACzD,YAAT,CAAsBzE,YAAtB,CAApB;;AACA,cAAI,CAACyI,aAAL,EAAoB;AAChBA,YAAAA,aAAa,GAAGP,QAAQ,CAACQ,YAAT,CAAsB1I,YAAtB,CAAhB;AACH,WAtDL,CAwDI;;;AACAyI,UAAAA,aAAa,CAACpG,IAAd,CAAmBC,KAAnB,EAA0BC,KAA1B,EAzDJ,CA2DI;;AACA,eAAKW,IAAL,CAAUuD,QAAV,CAAmByB,QAAnB,EA5DJ,CA8DI;;AACA,eAAK/C,eAAL,CAAqBwD,IAArB,CAA0BF,aAA1B,EA/DJ,CAiEI;;;AACA,eAAKhD,UAAL;AACA,eAAKD,aAAL,GAAqBoC,WAArB,CAnEJ,CAqEI;;AACA,cAAI9G,MAAJ,EAAY;AACR,iBAAK4E,aAAL,GAAqB,KAAKkD,QAAL,GAAgB,KAAKC,cAAL,CAAqB7G,GAArC,GAA2CS,IAAI,CAACqE,MAAL,MAAiB,KAAK+B,cAAL,CAAqB5G,GAArB,GAA2B,KAAK4G,cAAL,CAAqB7G,GAAjE,CAAhE;AACAqG,YAAAA,OAAO,CAACC,GAAR,CAAY,cAAZ,EAA4B,KAAK5C,aAAjC;AACH,WAHD,MAGO;AACH,iBAAKA,aAAL,GAAqB,KAAKkD,QAAL,GAAgB,KAAKC,cAAL,CAAqB7G,GAArC,GAA2C;AAAA;AAAA,oCAAQgF,aAAR,CAAsBF,MAAtB,MAAkC,KAAK+B,cAAL,CAAqB5G,GAArB,GAA2B,KAAK4G,cAAL,CAAqB7G,GAAlF,CAAhE;AACH,WA3EL,CA6EI;;;AACA,eAAK8G,iBAAL;AACH;;AAEOA,QAAAA,iBAAiB,GAAS;AAC9B,cAAI,CAAC,KAAKzC,QAAV,EAAoB;;AAEpB,kBAAQ,KAAKxE,IAAb;AACI,iBAAK;AAAA;AAAA,wDAAkBL,QAAvB;AACI;AACA,kBAAI,KAAK+D,UAAL,IAAmB,KAAKF,SAA5B,EAAuC;AACnC,qBAAKJ,OAAL,GAAetD,cAAc,CAACoH,GAA9B;AACH;;AACD;;AAEJ,iBAAK;AAAA;AAAA,wDAAkBtH,KAAvB;AACI;AACA,kBAAI,KAAKgE,UAAL,IAAmB,KAAKJ,SAA5B,EAAuC;AACnC,qBAAKJ,OAAL,GAAetD,cAAc,CAACoH,GAA9B;AACH;;AACD;;AAEJ,iBAAK;AAAA;AAAA,wDAAkBrH,KAAvB;AACI;AACA;AACA;AAEJ;AApBJ;;AAuBA,cAAI,KAAKuD,OAAL,KAAiBtD,cAAc,CAACoH,GAApC,EAAwC;AACpC,iBAAKC,eAAL;AACH;AACJ;;AAEOA,QAAAA,eAAe,GAAS;AAC5B,eAAKC,mBAAL;;AAEA,cAAI,CAACnI,MAAL,EAAa;AACT,iBAAKoC,IAAL,CAAUkB,OAAV;AACH,WAFD,MAEO;AACH;AACA,iBAAK6B,UAAL;AACH;AACJ;;AAEOA,QAAAA,UAAU,GAAS;AACvB,eAAKhB,OAAL,GAAetD,cAAc,CAACuD,QAA9B;AACA,eAAKO,UAAL,GAAkB,CAAlB;AACA,eAAKE,mBAAL,GAA2B,KAA3B;AACA,eAAKR,eAAL,GAAuB,EAAvB;AACA,eAAKI,UAAL,GAAkB,CAAlB;AACA,eAAKC,aAAL,GAAqB,CAArB;AACA,eAAKE,aAAL,GAAqB,CAArB;AACA,eAAKL,SAAL,GAAiB,CAAjB;AACA,eAAKD,SAAL,GAAiB,CAAjB;AACA,eAAKE,YAAL,GAAoB,CAApB;AACH;;AAEM4D,QAAAA,IAAI,CAACC,KAAD,EAAuB;AAC9B,cAAIrI,MAAJ,EAAY;AACR,gBAAIqI,KAAJ,EAAW;AACP,mBAAKlE,OAAL,GAAetD,cAAc,CAACgF,MAA9B;AACA,mBAAKD,aAAL;AACA2B,cAAAA,OAAO,CAACC,GAAR,CAAY,eAAZ;AACH,aAJD,MAIO;AACH,mBAAKrD,OAAL,GAAetD,cAAc,CAACuD,QAA9B;;AACA,mBAAK8D,eAAL;AACH;AACJ;AACJ;AAED;AACJ;AACA;;;AACYC,QAAAA,mBAAmB,GAAS;AAChC;AACA,eAAK,IAAMzB,IAAX,IAAmB,KAAKrC,eAAxB,EAAyC;AACrC,gBAAIqC,IAAI,CAACC,OAAT,EAAkB;AACdD,cAAAA,IAAI,CAACtE,IAAL,CAAUkB,OAAV;AACH;AACJ,WAN+B,CAQhC;;;AACA,eAAKe,eAAL,GAAuB,EAAvB;AACH;;AAESiE,QAAAA,SAAS,GAAS;AACxB,eAAKlG,IAAL,CAAUgD,iBAAV;AACH;;AA3S0C,O;;;;;iBAGf,K;;;;;;;iBAKK,I;;;;;;;iBAEf;AAAA;AAAA,8DAAsB4B,M;;;;;;;iBAEJ,E;;;;;;;iBAKtB;AAAA;AAAA,sDAAkBvG,Q;;;;;;;iBAET,C;;;;;;;iBAEuB,IAAIK,uBAAJ,E;;;;;;;iBAEnB,C;;;;;;;iBAEmB,IAAIA,uBAAJ,E;;;;;;;iBAEpB,C;;;;;;;iBAEuB,IAAIA,uBAAJ,E;;;;;;;iBAE1B,C;;;;;;;iBAEuB,IAAIA,uBAAJ,E;;;;;;;iBAEvB,C;;;;;;;iBAEuB,IAAIA,uBAAJ,E;;;;;;;iBAEJ,IAAIA,uBAAJ,E", "sourcesContent": ["import { _decorator, assetManager, Camera, CCBoolean, CCFloat, <PERSON><PERSON><PERSON><PERSON>, Component, director, Enum, instantiate, Prefab, Vec3, view } from 'cc';\r\nimport { EDITOR } from 'cc/env';\r\nimport { LayerEmittierStrategy, LayerEmittierType, LayerRandomRange } from '../../leveldata/leveldata';\r\nimport { GameIns } from '../GameIns';\r\nconst { ccclass, property, executeInEditMode } = _decorator;\r\n\r\nenum LayerEmittierTypeZh {\r\n    无限 = LayerEmittierType.Infinite,\r\n    持续时间 = LayerEmittierType.Duration,\r\n    发射次数 = LayerEmittierType.Count,\r\n    监听事件 = LayerEmittierType.Event\r\n}\r\n\r\nenum EmittierStatus {\r\n    inactive,\r\n    active,\r\n    pause,\r\n    end\r\n}\r\n\r\n@ccclass('SerializableRandomRange')\r\nexport class SerializableRandomRange {\r\n    @property({type: CCFloat, displayName: \"最小值\"})\r\n    public min: number = 0;\r\n    \r\n    @property({type: CCFloat, displayName: \"最大值\"})\r\n    public max: number = 0;\r\n    \r\n    // 提供转换方法\r\n    toLayerRandomRange(): LayerRandomRange {\r\n        return new LayerRandomRange(this.min, this.max);\r\n    }\r\n    \r\n    fromLayerRandomRange(range: LayerRandomRange): void {\r\n        this.min = range.min;\r\n        this.max = range.max;\r\n    }\r\n}\r\n\r\nexport class EmittierElem extends Component {\r\n    private _velocity: Vec3 = new Vec3(0, 0, 0);\r\n    \r\n    /**\r\n     * 初始化元素运动参数\r\n     * @param speed 运动速度\r\n     * @param angle 运动角度（度）\r\n     */\r\n    public init(speed: number, angle: number): void {\r\n        \r\n        // 将角度转换为弧度\r\n        const rad = angle * Math.PI / 180;\r\n        \r\n        // 计算速度分量\r\n        this._velocity.x = Math.cos(rad) * speed;\r\n        this._velocity.y = Math.sin(rad) * speed;\r\n    }\r\n    \r\n    tick(dt: number): void {\r\n        // 更新位置\r\n        const pos = this.node.position.clone();\r\n        pos.add(this._velocity.clone().multiplyScalar(dt));\r\n        this.node.setPosition(pos);\r\n        \r\n        // 检查是否超出屏幕边界\r\n        this._checkOutOfBounds();\r\n    }\r\n    \r\n    private _checkOutOfBounds(): void {\r\n        const mainCamera = this._findSceneCamera();\r\n        if (!mainCamera) return;\r\n        \r\n        // 2. 获取节点在世界坐标系中的位置\r\n        const worldPos = this.node.worldPosition;\r\n        \r\n        // 3. 将世界坐标转换为屏幕坐标\r\n        const screenPos = new Vec3();\r\n        mainCamera.worldToScreen(screenPos, worldPos);\r\n        \r\n        // 4. 获取屏幕尺寸\r\n        const screenSize = view.getVisibleSize();\r\n        \r\n        // 5. 设置边界缓冲值（避免元素刚接触边界就消失）\r\n        const buffer = 50;\r\n        \r\n        // 6. 检查是否超出屏幕边界\r\n        if (\r\n            screenPos.x < -buffer || \r\n            screenPos.x > screenSize.width + buffer ||\r\n            screenPos.y < -buffer || \r\n            screenPos.y > screenSize.height + buffer\r\n        ) {\r\n            // 销毁节点\r\n            this.node.destroy();\r\n        }\r\n    }\r\n\r\n    private _findSceneCamera(): Camera | null {\r\n            const scene = director.getScene();\r\n            if (!scene) return null;\r\n    \r\n            // Recursively search for camera components\r\n            const findCameraRecursive = (node: any): Camera | null => {\r\n                const camera = node.getComponent(Camera);\r\n                if (camera) return camera;\r\n    \r\n                for (const child of node.children) {\r\n                    const foundCamera = findCameraRecursive(child);\r\n                    if (foundCamera) return foundCamera;\r\n                }\r\n                return null;\r\n            };\r\n    \r\n            for (const rootNode of scene.children) {\r\n                const camera = findCameraRecursive(rootNode);\r\n                if (camera) return camera;\r\n            }\r\n    \r\n            return null;\r\n        }\r\n}\r\n\r\n@ccclass('EmittierTerrain')\r\n@executeInEditMode()\r\nexport class EmittierTerrain extends Component {\r\n\r\n    @property({visible: false})\r\n    private _bfollow: boolean = false;\r\n    @property({type: CCBoolean, displayName: \"是否跟随层级移动\"})\r\n    public set bfollow(value: boolean) { this._bfollow = value; }\r\n    public get bfollow(): boolean { return this._bfollow; }\r\n    @property({type: Prefab, displayName: \"发射器\"})\r\n    public emittier: Prefab | null = null;\r\n    @property({type: Enum(LayerEmittierStrategy), displayName: \"发射策略\"})\r\n    public strategy = LayerEmittierStrategy.Random;\r\n    @property({type: [Prefab], displayName: \"发射元素组\"})\r\n    public emittierElements: Prefab[] = [];\r\n    @property({type: Enum(LayerEmittierTypeZh), displayName:\"发射器类型\",})\r\n    public get typeZh(): LayerEmittierTypeZh { return this.type as unknown as LayerEmittierTypeZh;}\r\n    public set typeZh(value: LayerEmittierTypeZh) { this.type = value as unknown as LayerEmittierType;}\r\n    @property({visible: false})\r\n    public type = LayerEmittierType.Infinite;\r\n    @property({type: CCFloat, displayName: \"效果值\", tooltip: \"值对应类型:Infinite为无限,Duration为持续时间(ms),Count为发射次数,Event为监听事件\"})\r\n    public value: number = 0; // 根据type决定用途，Infinite为无限，Duration为持续时间，Count为发射次数，Event为监听事件\r\n    @property({type: SerializableRandomRange, displayName: \"效果值校正\"})\r\n    public valueModify: SerializableRandomRange = new SerializableRandomRange(); \r\n    @property({type: CCInteger, displayName: \"初始延迟(ms)\", min: 0})\r\n    public initDelay: number = 0;\r\n    @property({type: SerializableRandomRange, displayName: \"延迟校正(ms)\"})\r\n    public delayModify: SerializableRandomRange = new SerializableRandomRange();\r\n    @property({type: CCInteger, displayName: \"发射间隔(ms)\"})\r\n    public interval: number = 0;\r\n    @property({type: SerializableRandomRange, displayName: \"间隔校正(ms)\"})\r\n    public intervalModify: SerializableRandomRange = new SerializableRandomRange();\r\n    @property({type: CCInteger, displayName: \"发射角度(0-360)\",min: 0, max: 360})\r\n    public angle: number = 0;\r\n    @property({type: SerializableRandomRange, displayName: \"角度校正(0-360)\"})\r\n    public angleModify: SerializableRandomRange = new SerializableRandomRange();     \r\n    @property({type: CCFloat, displayName: \"发射速度\"})\r\n    public speed: number = 0;\r\n    @property({type: SerializableRandomRange, displayName: \"速度校正\"})\r\n    public speedModify: SerializableRandomRange = new SerializableRandomRange();\r\n    @property({type: SerializableRandomRange, displayName:\"X偏移范围\"})\r\n    public offSetX: SerializableRandomRange = new SerializableRandomRange();\r\n\r\n    private _status: EmittierStatus = EmittierStatus.inactive;\r\n    private _activeElements: EmittierElem[] = [];\r\n\r\n    private _curDelay: number = 0; // 当前延迟\r\n    private _curValue: number = 0; // 当前效果值\r\n    private _curEmiIndex: number = 0; // 当前发射器索引\r\n\r\n    private _deltaTime = 0; // 发射器运行总时间\r\n    private _lastEmitTime = 0; // 上次发射时间\r\n    private _emitCount: number = 0; // 已发射元素计数\r\n    private _nextInterval: number = 0; // 下一次发射的间隔时间\r\n    private _initialDelayPassed: boolean = false; // 初始延迟是否已过\r\n\r\n    protected onLoad(): void {\r\n        this._resetData();\r\n\r\n        if (EDITOR) {\r\n            this.node.removeAllChildren();\r\n            assetManager.loadAny({ uuid: this.emittier!.uuid }, (err, prefab: Prefab) => {\r\n                if (err) {\r\n                    return;\r\n                } else {\r\n                    \r\n                    const emitterNode = instantiate(prefab);    \r\n                    this.node.addChild(emitterNode);\r\n                }\r\n            });\r\n        }\r\n    }\r\n\r\n    public startEmittier(): void {\r\n        this._status = EmittierStatus.active;\r\n        if (EDITOR) {\r\n            this._curDelay = this.initDelay + this.delayModify!.min + Math.random() * (this.delayModify!.max - this.delayModify!.min);\r\n            this._curValue = this.value + Math.random() * (this.valueModify!.max - this.valueModify!.min);\r\n        } else {\r\n            this._curDelay = this.initDelay + this.delayModify!.min + GameIns.battleManager.random() * (this.delayModify!.max - this.delayModify!.min);\r\n            this._curValue = this.value + this.valueModify!.min + GameIns.battleManager.random() * (this.valueModify!.max - this.valueModify!.min);\r\n        }\r\n    }\r\n\r\n    public tick(dt: number): void {\r\n        if (this._status !== EmittierStatus.active) return;\r\n\r\n        const dtMs = dt * 1000;\r\n        // 运行发射器\r\n        this._updateEmitter(dtMs);\r\n        this._updateActiveElements(dt);\r\n    }\r\n\r\n    /**\r\n     * 更新发射器自身状态\r\n     * @param dt 增量时间（豪秒）\r\n     */\r\n    private _updateEmitter(dt: number): void {\r\n        this._deltaTime += dt;\r\n        \r\n        if (!this._initialDelayPassed) {\r\n            const delaySeconds = this._curDelay / 1000;\r\n            if (this._deltaTime >= delaySeconds) {\r\n                this._initialDelayPassed = true;\r\n                this._deltaTime = 0; // 重置时间，开始发射循环\r\n            }\r\n            return;\r\n        }\r\n        \r\n        // 运行发射器\r\n        this._runEmittier();\r\n    }\r\n\r\n    /**\r\n     * 更新所有已发射的元素\r\n     * @param dt 增量时间（秒）\r\n     */\r\n    private _updateActiveElements(dt: number): void {\r\n        // 遍历所有元素并更新\r\n        for (let i = this._activeElements.length - 1; i >= 0; i--) {\r\n            const elem = this._activeElements[i];\r\n            \r\n            // 检查元素是否已被销毁\r\n            if (!elem.isValid) {\r\n                // 从数组中移除已销毁的元素\r\n                this._activeElements.splice(i, 1);\r\n                continue;\r\n            }\r\n            \r\n            // 更新元素状态\r\n            elem.tick(dt);\r\n        }\r\n    }\r\n\r\n    private _runEmittier(): void \r\n    {\r\n        if (!this.emittier || this.emittierElements.length === 0) return;\r\n        \r\n        const currentTime = this._deltaTime;\r\n        \r\n        // 检查是否达到发射间隔\r\n        if (currentTime - this._lastEmitTime < this._nextInterval) return;\r\n        \r\n        // 随机选择要发射的元素\r\n        if (this.strategy === LayerEmittierStrategy.Random) { \r\n            if (EDITOR) {\r\n                this._curEmiIndex = Math.floor(Math.random() * this.emittierElements.length);\r\n            } else {\r\n                this._curEmiIndex = Math.floor(GameIns.battleManager.random() * this.emittierElements.length);\r\n            }\r\n        } else if (this.strategy === LayerEmittierStrategy.Sequence) {\r\n            this._curEmiIndex = this._emitCount % this.emittierElements.length;\r\n        }\r\n        const elemPrefab = this.emittierElements[this._curEmiIndex];\r\n        \r\n        // 实例化元素\r\n        const elemNode = instantiate(elemPrefab);\r\n        \r\n        // 设置初始位置（考虑随机偏移）\r\n        let offsetX = 0;\r\n        if (EDITOR) {\r\n            offsetX = this.offSetX!.min + Math.random() * (this.offSetX!.max - this.offSetX!.min);\r\n            console.log(\"随机地形 x坐标偏移：\", offsetX);\r\n        } else {\r\n            offsetX = GameIns.battleManager.random() * (this.offSetX!.max - this.offSetX!.min);\r\n        }\r\n        elemNode.setPosition(this.node.position.x + offsetX, 0, 0);\r\n        \r\n        // 设置初始角度（考虑随机偏移）\r\n        let angle = 0;\r\n        if (EDITOR) {\r\n            angle = this.angle + this.angleModify!.min + Math.random() * (this.angleModify!.max - this.angleModify!.min);\r\n        } else {\r\n            angle = this.angle + this.angleModify!.min + GameIns.battleManager.random() * (this.angleModify!.max - this.angleModify!.min);\r\n        }\r\n        \r\n        // 设置初始速度（考虑随机偏移）\r\n        let speed = 0;\r\n        if (EDITOR) {\r\n            speed = this.speed + this.speedModify!.min + Math.random() * (this.speedModify!.max - this.speedModify!.min);\r\n            console.log(\"随机地形 发射速度：\", speed);\r\n        } else {\r\n            speed = this.speed + this.speedModify!.min + GameIns.battleManager.random() * (this.speedModify!.max - this.speedModify!.min);\r\n        }\r\n        \r\n        // 获取或添加EmittierElem组件\r\n        let elemComponent = elemNode.getComponent(EmittierElem);\r\n        if (!elemComponent) {\r\n            elemComponent = elemNode.addComponent(EmittierElem);\r\n        }\r\n        \r\n        // 初始化元素运动\r\n        elemComponent.init(speed, angle);\r\n        \r\n        // 添加到场景\r\n        this.node.addChild(elemNode);\r\n\r\n        // 添加到活动元素列表\r\n        this._activeElements.push(elemComponent);\r\n         \r\n        // 更新发射计数和时间\r\n        this._emitCount++;\r\n        this._lastEmitTime = currentTime;\r\n        \r\n        // 计算下一次发射的间隔时间\r\n        if (EDITOR) {\r\n            this._nextInterval = this.interval + this.intervalModify!.min + Math.random() * (this.intervalModify!.max - this.intervalModify!.min);\r\n            console.log(\"随机地形 下次发射间隔：\", this._nextInterval);\r\n        } else {\r\n            this._nextInterval = this.interval + this.intervalModify!.min + GameIns.battleManager.random() * (this.intervalModify!.max - this.intervalModify!.min);            \r\n        }\r\n        \r\n        // 检查发射器是否结束\r\n        this._checkEmittierEnd();\r\n    }\r\n\r\n    private _checkEmittierEnd(): void {\r\n        if (!this.emittier) return;\r\n        \r\n        switch (this.type) {\r\n            case LayerEmittierType.Duration:\r\n                // 持续时间结束\r\n                if (this._deltaTime >= this._curValue) {\r\n                    this._status = EmittierStatus.end;\r\n                }\r\n                break;\r\n                \r\n            case LayerEmittierType.Count:\r\n                // 发射次数达到\r\n                if (this._emitCount >= this._curValue) {\r\n                    this._status = EmittierStatus.end;\r\n                }\r\n                break;\r\n                \r\n            case LayerEmittierType.Event:\r\n                // 事件触发结束（需要外部实现）\r\n                // 这里可以添加事件监听逻辑\r\n                break;\r\n                \r\n            // 无限类型不需要结束检查\r\n        }\r\n\r\n        if (this._status === EmittierStatus.end){\r\n            this._destroyEmitter();\r\n        }\r\n    }\r\n\r\n    private _destroyEmitter(): void {\r\n        this._destroyAllElements();\r\n\r\n        if (!EDITOR) {\r\n            this.node.destroy();\r\n        } else {\r\n            // 在编辑器中，我们只重置状态，不实际销毁节点\r\n            this._resetData();\r\n        }\r\n    }\r\n\r\n    private _resetData(): void {\r\n        this._status = EmittierStatus.inactive;\r\n        this._emitCount = 0;\r\n        this._initialDelayPassed = false;\r\n        this._activeElements = [];\r\n        this._deltaTime = 0;\r\n        this._lastEmitTime = 0;\r\n        this._nextInterval = 0;\r\n        this._curValue = 0;\r\n        this._curDelay = 0;\r\n        this._curEmiIndex = 0;\r\n    }\r\n\r\n    public play(bPlay: boolean): void {\r\n        if (EDITOR) {\r\n            if (bPlay) {\r\n                this._status = EmittierStatus.active;\r\n                this.startEmittier();\r\n                console.log(\"play emittier\");\r\n            } else {\r\n                this._status = EmittierStatus.inactive;\r\n                this._destroyEmitter();\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 销毁所有已发射的元素\r\n     */\r\n    private _destroyAllElements(): void {\r\n        // 销毁所有元素节点\r\n        for (const elem of this._activeElements) {\r\n            if (elem.isValid) {\r\n                elem.node.destroy();\r\n            }\r\n        }\r\n        \r\n        // 清空元素列表\r\n        this._activeElements = [];\r\n    }\r\n\r\n    protected onDestroy(): void {\r\n        this.node.removeAllChildren();\r\n    }\r\n}\r\n\r\n"]}