2025-9-22 11:59:52-log: Cannot access game frame or container.
2025-9-22 11:59:52-debug: asset-db:require-engine-code (410ms)
2025-9-22 11:59:52-log: meshopt wasm decoder initialized
2025-9-22 11:59:52-log: [bullet]:bullet wasm lib loaded.
2025-9-22 11:59:52-log: [box2d]:box2d wasm lib loaded.
2025-9-22 11:59:52-log: Cocos Creator v3.8.6
2025-9-22 11:59:52-log: Using legacy pipeline
2025-9-22 11:59:52-debug: [Assets Memory track]: asset-db-plugin-register: programming start:80.91MB, end 84.25MB, increase: 3.34MB
2025-9-22 11:59:52-debug: [Assets Memory track]: asset-db:worker-init: initEngine start:30.88MB, end 80.00MB, increase: 49.13MB
2025-9-22 11:59:53-debug: [Assets Memory track]: asset-db-plugin-register: builder start:84.28MB, end 290.63MB, increase: 206.34MB
2025-9-22 11:59:52-log: Forward render pipeline initialized.
2025-9-22 11:59:53-debug: [Assets Memory track]: asset-db:worker-init: initPlugin start:80.03MB, end 289.10MB, increase: 209.07MB
2025-9-22 11:59:53-debug: [Assets Memory track]: asset-db-plugin-register: project start:80.77MB, end 289.07MB, increase: 208.30MB
2025-9-22 11:59:53-debug: run package(google-play) handler(enable) success!
2025-9-22 11:59:53-debug: run package(google-play) handler(enable) start
2025-9-22 11:59:53-debug: run package(harmonyos-next) handler(enable) start
2025-9-22 11:59:53-debug: run package(honor-mini-game) handler(enable) start
2025-9-22 11:59:53-debug: run package(harmonyos-next) handler(enable) success!
2025-9-22 11:59:53-debug: run package(honor-mini-game) handler(enable) success!
2025-9-22 11:59:53-debug: run package(huawei-agc) handler(enable) success!
2025-9-22 11:59:53-debug: run package(huawei-agc) handler(enable) start
2025-9-22 11:59:53-debug: run package(ios) handler(enable) success!
2025-9-22 11:59:53-debug: run package(huawei-quick-game) handler(enable) success!
2025-9-22 11:59:53-debug: run package(ios) handler(enable) start
2025-9-22 11:59:53-debug: run package(linux) handler(enable) start
2025-9-22 11:59:53-debug: run package(huawei-quick-game) handler(enable) start
2025-9-22 11:59:53-debug: run package(linux) handler(enable) success!
2025-9-22 11:59:53-debug: run package(mac) handler(enable) start
2025-9-22 11:59:53-debug: run package(migu-mini-game) handler(enable) success!
2025-9-22 11:59:53-debug: run package(migu-mini-game) handler(enable) start
2025-9-22 11:59:53-debug: run package(native) handler(enable) success!
2025-9-22 11:59:53-debug: run package(native) handler(enable) start
2025-9-22 11:59:53-debug: run package(ohos) handler(enable) start
2025-9-22 11:59:53-debug: run package(ohos) handler(enable) success!
2025-9-22 11:59:53-debug: run package(mac) handler(enable) success!
2025-9-22 11:59:53-debug: run package(oppo-mini-game) handler(enable) start
2025-9-22 11:59:53-debug: run package(runtime-dev-tools) handler(enable) start
2025-9-22 11:59:53-debug: run package(oppo-mini-game) handler(enable) success!
2025-9-22 11:59:53-debug: run package(runtime-dev-tools) handler(enable) success!
2025-9-22 11:59:53-debug: run package(vivo-mini-game) handler(enable) start
2025-9-22 11:59:53-debug: run package(taobao-mini-game) handler(enable) start
2025-9-22 11:59:53-debug: run package(vivo-mini-game) handler(enable) success!
2025-9-22 11:59:53-debug: run package(taobao-mini-game) handler(enable) success!
2025-9-22 11:59:53-debug: run package(web-mobile) handler(enable) success!
2025-9-22 11:59:53-debug: run package(web-desktop) handler(enable) start
2025-9-22 11:59:53-debug: run package(web-desktop) handler(enable) success!
2025-9-22 11:59:53-debug: run package(wechatgame) handler(enable) success!
2025-9-22 11:59:53-debug: run package(web-mobile) handler(enable) start
2025-9-22 11:59:53-debug: run package(wechatgame) handler(enable) start
2025-9-22 11:59:53-debug: run package(wechatprogram) handler(enable) success!
2025-9-22 11:59:53-debug: run package(wechatprogram) handler(enable) start
2025-9-22 11:59:53-debug: run package(windows) handler(enable) success!
2025-9-22 11:59:53-debug: run package(windows) handler(enable) start
2025-9-22 11:59:53-debug: run package(xiaomi-quick-game) handler(enable) start
2025-9-22 11:59:53-debug: run package(cocos-service) handler(enable) success!
2025-9-22 11:59:53-debug: run package(xiaomi-quick-game) handler(enable) success!
2025-9-22 11:59:53-debug: run package(cocos-service) handler(enable) start
2025-9-22 11:59:53-debug: run package(im-plugin) handler(enable) start
2025-9-22 11:59:53-debug: run package(im-plugin) handler(enable) success!
2025-9-22 11:59:53-debug: run package(fix-asset-default-userdata) handler(enable) start
2025-9-22 11:59:53-debug: run package(fix-asset-default-userdata) handler(enable) success!
2025-9-22 11:59:53-debug: asset-db:worker-init: initPlugin (1013ms)
2025-9-22 11:59:53-debug: [Assets Memory track]: asset-db:worker-init start:30.87MB, end 291.29MB, increase: 260.42MB
2025-9-22 11:59:53-debug: Run asset db hook programming:beforePreStart success!
2025-9-22 11:59:53-debug: Run asset db hook programming:beforePreStart ...
2025-9-22 11:59:53-debug: Run asset db hook engine-extends:beforePreStart success!
2025-9-22 11:59:53-debug: Run asset db hook engine-extends:beforePreStart ...
2025-9-22 11:59:53-debug: run package(emitter-editor) handler(enable) start
2025-9-22 11:59:53-debug: run package(emitter-editor) handler(enable) success!
2025-9-22 11:59:53-debug: start refresh asset from db://assets/editor/enum-gen/EmitterEnum.ts...
2025-9-22 11:59:53-debug: refresh asset db://assets/editor/enum-gen success
2025-9-22 11:59:53-debug: start refresh asset from db://assets/editor/enum-gen/EnemyEnum.ts...
2025-9-22 11:59:53-debug: refresh asset db://assets/editor/enum-gen success
2025-9-22 11:59:53-debug: asset-db:worker-init (1534ms)
2025-9-22 11:59:53-debug: asset-db-hook-programming-beforePreStart (50ms)
2025-9-22 11:59:53-debug: asset-db-hook-engine-extends-beforePreStart (50ms)
2025-9-22 11:59:53-debug: run package(level-editor) handler(enable) start
2025-9-22 11:59:53-debug: run package(level-editor) handler(enable) success!
2025-9-22 11:59:53-debug: Preimport db internal success
2025-9-22 11:59:53-debug: %cImport%c: E:\M2Game\Client\assets\editor\enum-gen\EmitterEnum.ts
background: #aaff85; color: #000;
color: #000;
2025-9-22 11:59:53-debug: %cImport%c: E:\M2Game\Client\assets\editor\enum-gen\EnemyEnum.ts
background: #aaff85; color: #000;
color: #000;
2025-9-22 11:59:53-debug: run package(localization-editor) handler(enable) start
2025-9-22 11:59:53-debug: run package(localization-editor) handler(enable) success!
2025-9-22 11:59:53-debug: Preimport db assets success
2025-9-22 11:59:53-debug: Run asset db hook programming:afterPreStart ...
2025-9-22 11:59:53-debug: starting packer-driver...
2025-9-22 11:59:53-debug: run package(wave-editor) handler(enable) start
2025-9-22 11:59:53-debug: run package(wave-editor) handler(enable) success!
2025-9-22 11:59:53-debug: run package(placeholder) handler(enable) start
2025-9-22 11:59:53-debug: run package(placeholder) handler(enable) success!
2025-9-22 12:00:00-debug: initialize scripting environment...
2025-9-22 12:00:00-debug: [[Executor]] prepare before lock
2025-9-22 12:00:00-debug: Set detail map pack:///resolution-detail-map.json: {
  "./chunks/79/7983294ee03fd2619ed97c3289fe6adabb609214.js": {
    "__unresolved_2": {
      "error": "Error: 以 file:///E:/M2Game/Client/assets/bundles/common/script/game/data/BossData.ts 为起点找不到模块 \"./EnemyWave\"",
      "messages": [
        {
          "level": "warn",
          "text": "你是否遗漏了扩展名？请注意你不能在模块说明符中省略扩展名。"
        }
      ]
    }
  }
}

2025-9-22 12:00:00-debug: Run asset db hook engine-extends:afterPreStart ...
2025-9-22 12:00:00-debug: Run asset db hook programming:afterPreStart success!
2025-9-22 12:00:00-debug: [[Executor]] prepare after unlock
2025-9-22 12:00:00-debug: Run asset db hook engine-extends:afterPreStart success!
2025-9-22 12:00:00-debug: Start up the 'internal' database...
2025-9-22 12:00:00-debug: asset-db-hook-programming-afterPreStart (6423ms)
2025-9-22 12:00:00-debug: asset-db:worker-effect-data-processing (205ms)
2025-9-22 12:00:00-debug: asset-db-hook-engine-extends-afterPreStart (205ms)
2025-9-22 12:00:00-debug: Start up the 'assets' database...
2025-9-22 12:00:00-debug: asset-db:worker-startup-database[internal] (6673ms)
2025-9-22 12:00:00-debug: [Assets Memory track]: asset-db:worker-init: startup start:178.04MB, end 194.32MB, increase: 16.28MB
2025-9-22 12:00:00-debug: lazy register asset handler *
2025-9-22 12:00:00-debug: lazy register asset handler text
2025-9-22 12:00:00-debug: lazy register asset handler directory
2025-9-22 12:00:00-debug: lazy register asset handler dragonbones
2025-9-22 12:00:00-debug: lazy register asset handler spine-data
2025-9-22 12:00:00-debug: lazy register asset handler dragonbones-atlas
2025-9-22 12:00:00-debug: lazy register asset handler json
2025-9-22 12:00:00-debug: lazy register asset handler javascript
2025-9-22 12:00:00-debug: lazy register asset handler scene
2025-9-22 12:00:00-debug: lazy register asset handler terrain
2025-9-22 12:00:00-debug: lazy register asset handler prefab
2025-9-22 12:00:00-debug: lazy register asset handler typescript
2025-9-22 12:00:00-debug: lazy register asset handler sprite-frame
2025-9-22 12:00:00-debug: lazy register asset handler tiled-map
2025-9-22 12:00:00-debug: lazy register asset handler buffer
2025-9-22 12:00:00-debug: lazy register asset handler texture
2025-9-22 12:00:00-debug: lazy register asset handler alpha-image
2025-9-22 12:00:00-debug: lazy register asset handler texture-cube
2025-9-22 12:00:00-debug: lazy register asset handler sign-image
2025-9-22 12:00:00-debug: lazy register asset handler render-texture
2025-9-22 12:00:00-debug: lazy register asset handler erp-texture-cube
2025-9-22 12:00:00-debug: lazy register asset handler texture-cube-face
2025-9-22 12:00:00-debug: lazy register asset handler gltf
2025-9-22 12:00:00-debug: lazy register asset handler rt-sprite-frame
2025-9-22 12:00:00-debug: lazy register asset handler gltf-animation
2025-9-22 12:00:00-debug: lazy register asset handler image
2025-9-22 12:00:00-debug: lazy register asset handler gltf-mesh
2025-9-22 12:00:00-debug: lazy register asset handler gltf-skeleton
2025-9-22 12:00:00-debug: lazy register asset handler gltf-embeded-image
2025-9-22 12:00:00-debug: lazy register asset handler gltf-material
2025-9-22 12:00:00-debug: lazy register asset handler gltf-scene
2025-9-22 12:00:00-debug: lazy register asset handler fbx
2025-9-22 12:00:00-debug: lazy register asset handler physics-material
2025-9-22 12:00:00-debug: lazy register asset handler effect-header
2025-9-22 12:00:00-debug: lazy register asset handler effect
2025-9-22 12:00:00-debug: lazy register asset handler animation-clip
2025-9-22 12:00:00-debug: lazy register asset handler material
2025-9-22 12:00:00-debug: lazy register asset handler animation-graph
2025-9-22 12:00:00-debug: lazy register asset handler audio-clip
2025-9-22 12:00:00-debug: lazy register asset handler animation-graph-variant
2025-9-22 12:00:00-debug: lazy register asset handler ttf-font
2025-9-22 12:00:00-debug: lazy register asset handler animation-mask
2025-9-22 12:00:00-debug: lazy register asset handler particle
2025-9-22 12:00:00-debug: lazy register asset handler bitmap-font
2025-9-22 12:00:00-debug: lazy register asset handler render-pipeline
2025-9-22 12:00:00-debug: lazy register asset handler label-atlas
2025-9-22 12:00:00-debug: lazy register asset handler auto-atlas
2025-9-22 12:00:00-debug: lazy register asset handler render-flow
2025-9-22 12:00:00-debug: lazy register asset handler render-stage
2025-9-22 12:00:00-debug: lazy register asset handler instantiation-material
2025-9-22 12:00:00-debug: lazy register asset handler sprite-atlas
2025-9-22 12:00:00-debug: lazy register asset handler instantiation-mesh
2025-9-22 12:00:00-debug: lazy register asset handler instantiation-skeleton
2025-9-22 12:00:00-debug: lazy register asset handler video-clip
2025-9-22 12:00:00-debug: lazy register asset handler instantiation-animation
2025-9-22 12:00:00-debug: asset-db:start-database (6743ms)
2025-9-22 12:00:00-debug: fix the bug of updateDefaultUserData
2025-9-22 12:00:00-debug: asset-db:ready (10117ms)
2025-9-22 12:00:00-debug: init worker message success
2025-9-22 12:00:00-debug: asset-db:worker-startup-database[assets] (6669ms)
2025-9-22 12:00:00-debug: programming:execute-script (3ms)
2025-9-22 12:00:00-debug: [Build Memory track]: builder:worker-init start:198.94MB, end 211.21MB, increase: 12.28MB
2025-9-22 12:00:00-debug: builder:worker-init (271ms)
2025-9-22 12:00:19-debug: refresh db internal success
2025-9-22 12:00:20-debug: refresh db assets success
2025-9-22 12:00:20-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-22 12:00:20-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-22 12:00:20-debug: asset-db:refresh-all-database (125ms)
2025-9-22 12:00:20-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-22 12:00:20-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-22 12:00:30-debug: refresh db internal success
2025-9-22 12:00:30-debug: refresh db assets success
2025-9-22 12:00:30-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-22 12:00:30-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-22 12:00:30-debug: asset-db:refresh-all-database (127ms)
2025-9-22 12:00:30-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-22 12:00:30-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-22 12:01:23-debug: refresh db internal success
2025-9-22 12:01:23-debug: refresh db assets success
2025-9-22 12:01:23-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-22 12:01:23-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-22 12:01:23-debug: asset-db:refresh-all-database (159ms)
2025-9-22 12:01:23-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
