{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/weapon/Weapon.ts"], "names": ["_decorator", "eEmitterStatus", "Emitter", "Entity", "ccclass", "property", "Weapon", "displayName", "type", "visible", "useCondition", "eWeaponUseCond", "WeaponDestroyed", "m_state", "eWeaponState", "None", "m_stateElapsedTime", "m_target", "m_owner", "init", "emitter", "changeStatus", "state", "value", "<PERSON><PERSON><PERSON><PERSON>", "owner", "<PERSON><PERSON><PERSON><PERSON>", "target", "Immediate", "activate", "tick", "dt", "Aiming", "tickAiming", "Emitting", "tickEmitting", "aimingTime", "turn<PERSON>o<PERSON>arget", "status", "tickNone", "weapon", "targetWeapons", "isDead", "ownerNode", "node", "targetNode", "angle", "targetAngle", "Math", "atan2", "worldPosition", "y", "x", "PI", "deltaAngle", "max<PERSON><PERSON><PERSON>", "turnSpeed", "abs", "sign"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGSA,MAAAA,U,OAAAA,U;;AAHAC,MAAAA,c,iBAAAA,c;AAAgBC,MAAAA,O,iBAAAA,O;;AAClBC,MAAAA,M;;;;;;;;;OAGD;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBL,U;;;;;;;;;;;;;AAgB9B;AACA;AACA;;;wBAEaM,M,WADZF,OAAO,CAAC,QAAD,C,UAEHC,QAAQ,CAAC;AAAEE,QAAAA,WAAW,EAAE;AAAf,OAAD,C,UAERF,QAAQ,CAAC;AAAEE,QAAAA,WAAW,EAAE;AAAf,OAAD,C,UAERF,QAAQ,CAAC;AAAEE,QAAAA,WAAW,EAAE;AAAf,OAAD,C,UAERF,QAAQ,CAAC;AAAEG,QAAAA,IAAI,EAAE,CAACF,MAAD,CAAR;AAAkBC,QAAAA,WAAW,EAAE,MAA/B;;AACNE,QAAAA,OAAO,GAAG;AACN;AACA,iBAAO,KAAKC,YAAL,IAAqBC,cAAc,CAACC,eAA3C;AACH;;AAJK,OAAD,C,UAQRP,QAAQ,CAAC;AAAEG,QAAAA,IAAI;AAAA;AAAA,8BAAN;AAAiBD,QAAAA,WAAW,EAAE;AAA9B,OAAD,C,2BAhBb,MACaD,MADb;AAAA;AAAA,4BACmC;AAAA;AAAA;;AAAA;;AAES;AAFT;;AAIY;AAJZ;;AAAA;;AAAA;;AAgBQ;AAhBR,eAkBvBO,OAlBuB,GAkBCC,YAAY,CAACC,IAlBd;AAAA,eAmBvBC,kBAnBuB,GAmBM,CAnBN;AAmBS;AAnBT,eAoBvBC,QApBuB,GAoBG,IApBH;AAoBS;AApBT,eAqBvBC,OArBuB,GAqBE,IArBF;AAAA;;AAqBS;AAExCC,QAAAA,IAAI,GAAG;AACH,eAAKN,OAAL,GAAeC,YAAY,CAACC,IAA5B;;AACA,cAAI,KAAKK,OAAT,EAAkB;AACd,iBAAKA,OAAL,CAAaC,YAAb,CAA0B;AAAA;AAAA,kDAAeN,IAAzC;AACH;AACJ;;AAEe,YAALO,KAAK,GAAiB;AAC7B,iBAAO,KAAKT,OAAZ;AACH;;AAEe,YAALS,KAAK,CAACC,KAAD,EAAsB;AAClC,cAAI,KAAKV,OAAL,KAAiBU,KAArB,EAA4B;AACxB;AACH;;AAED,eAAKV,OAAL,GAAeU,KAAf;AACA,eAAKP,kBAAL,GAA0B,CAA1B;AACH;;AAEMQ,QAAAA,QAAQ,CAACC,KAAD,EAAwB;AACnC,eAAKP,OAAL,GAAeO,KAAf;AACA,iBAAO,IAAP;AACH;;AAEMC,QAAAA,SAAS,CAACC,MAAD,EAAgC;AAC5C,eAAKV,QAAL,GAAgBU,MAAhB;;AACA,cAAI,KAAKjB,YAAL,KAAsBC,cAAc,CAACiB,SAAzC,EAAoD;AAChD,iBAAKC,QAAL;AACH;;AAED,iBAAO,IAAP;AACH;;AAEMC,QAAAA,IAAI,CAACC,EAAD,EAAa;AACpB,eAAKf,kBAAL,IAA2Be,EAA3B;;AACA,kBAAQ,KAAKlB,OAAb;AACI,iBAAKC,YAAY,CAACkB,MAAlB;AACI,mBAAKC,UAAL,CAAgBF,EAAhB;AACA;;AACJ,iBAAKjB,YAAY,CAACoB,QAAlB;AACI,mBAAKC,YAAL,CAAkBJ,EAAlB;AACA;;AACJ,iBAAKjB,YAAY,CAACC,IAAlB;AAEI;AATR;AAWH,SAtE8B,CAwE/B;;;AACQc,QAAAA,QAAQ,GAAG;AACf,eAAKP,KAAL,GAAa,KAAKc,UAAL,GAAkB,CAAlB,GAAsBtB,YAAY,CAACkB,MAAnC,GAA4ClB,YAAY,CAACoB,QAAtE;AACH;;AAEOD,QAAAA,UAAU,CAACF,EAAD,EAAa;AAC3B,cAAI,KAAKf,kBAAL,IAA2B,KAAKoB,UAApC,EAAgD;AAC5C,iBAAKd,KAAL,GAAaR,YAAY,CAACoB,QAA1B;AACH;;AAED,eAAKG,YAAL,CAAkBN,EAAlB;AACH;;AAEOI,QAAAA,YAAY,CAACJ,EAAD,EAAa;AAC7B,cAAI,KAAKX,OAAL,IAAgB,KAAKA,OAAL,CAAakB,MAAb,KAAwB;AAAA;AAAA,gDAAeJ,QAA3D,EAAqE;AACjE,iBAAKd,OAAL,CAAaC,YAAb,CAA0B;AAAA;AAAA,kDAAea,QAAzC;AACH;AACJ;;AAEOK,QAAAA,QAAQ,CAACR,EAAD,EAAa;AACzB,cAAI,KAAKrB,YAAL,KAAsBC,cAAc,CAACC,eAAzC,EAA0D;AACtD,iBAAK,IAAI4B,MAAT,IAAmB,KAAKC,aAAxB,EAAuC;AACnC,kBAAI,CAACD,MAAM,CAACE,MAAZ,EAAoB;AAChB;AACH;AACJ;;AACD,iBAAKb,QAAL;AACH;AACJ;;AAEOQ,QAAAA,YAAY,CAACN,EAAD,EAAa;AAC7B,cAAI,CAAC,KAAKd,QAAN,IAAkB,CAAC,KAAKC,OAA5B,EAAqC;AACjC;AACH;;AACD,cAAMyB,SAAS,GAAG,KAAKzB,OAAL,CAAa0B,IAA/B;AACA,cAAMC,UAAU,GAAG,KAAK5B,QAAL,CAAc2B,IAAjC;AACA,cAAME,KAAK,GAAGH,SAAS,CAACG,KAAxB;AACA,cAAMC,WAAW,GAAGC,IAAI,CAACC,KAAL,CAAWJ,UAAU,CAACK,aAAX,CAAyBC,CAAzB,GAA6BR,SAAS,CAACO,aAAV,CAAwBC,CAAhE,EAAmEN,UAAU,CAACK,aAAX,CAAyBE,CAAzB,GAA6BT,SAAS,CAACO,aAAV,CAAwBE,CAAxH,IAA6H,GAA7H,GAAmIJ,IAAI,CAACK,EAAxI,GAA6I,EAAjK;AACA,cAAIC,UAAU,GAAGP,WAAW,GAAGD,KAA/B;;AACA,cAAIQ,UAAU,GAAG,GAAjB,EAAsB;AAClBA,YAAAA,UAAU,IAAI,GAAd;AACH,WAFD,MAEO,IAAIA,UAAU,GAAG,CAAC,GAAlB,EAAuB;AAC1BA,YAAAA,UAAU,IAAI,GAAd;AACH;;AACD,cAAMC,QAAQ,GAAG,KAAKC,SAAL,GAAiBzB,EAAjB,GAAsB,IAAvC;;AACA,cAAIiB,IAAI,CAACS,GAAL,CAASH,UAAT,KAAwBC,QAA5B,EAAsC;AAClCZ,YAAAA,SAAS,CAACG,KAAV,GAAkBC,WAAlB;AACH,WAFD,MAGK;AACDJ,YAAAA,SAAS,CAACG,KAAV,IAAmBE,IAAI,CAACU,IAAL,CAAUJ,UAAV,IAAwBC,QAA3C;AACH;AACJ;;AA3H8B,O;;;;;iBAEK,E;;;;;;;iBAEC,I;;;;;;;iBAEU5C,cAAc,CAACiB,S;;;;;;;iBAO7B,E;;;;;;;iBAGA,I", "sourcesContent": ["import { eEmitterStatus, Emitter } from \"db://assets/bundles/common/script/game/bullet/Emitter\";\r\nimport Entity from \"db://assets/bundles/common/script/game/ui/base/Entity\";\r\n\r\nimport { _decorator, Component } from \"cc\";\r\nconst { ccclass, property } = _decorator;\r\n\r\nexport const enum eWeaponUseCond {\r\n    Immediate,        // 立刻启用\r\n    DelayTime,        // 延迟一段时间启用\r\n    WeaponDestroyed,  // 前置武器被销毁\r\n    // TODO: TargetDistance, // 目标距离在范围内\r\n    // TODO: TargetAngle,    // 目标角度在范围内\r\n}\r\n\r\nexport const enum eWeaponState {\r\n    Aiming,     // 玩家飞机不需要这个状态, 只有敌机需要\r\n    Emitting,   // 发射中\r\n    None,       // 无任何状态:后面看需求是否增加Destroyed等状态\r\n}\r\n\r\n/**\r\n * 武器后续可能也需要继承自PlaneBase, 因为武器可能也需要血量，扣血等逻辑。\r\n */\r\n@ccclass('Weapon')\r\nexport class Weapon extends Entity {\r\n    @property({ displayName: \"转向速度\" })\r\n    public readonly turnSpeed: number = 60; // 转向速度（仅用在追踪目标时）\r\n    @property({ displayName: \"锁定目标时间\" })\r\n    public readonly aimingTime: number = 2000; // 锁定目标时间，单位ms\r\n    @property({ displayName: \"启用条件\"})\r\n    public readonly useCondition: eWeaponUseCond = eWeaponUseCond.Immediate;\r\n    @property({ type: [Weapon], displayName: \"目标武器\", \r\n        visible() { \r\n            // @ts-ignore\r\n            return this.useCondition == eWeaponUseCond.WeaponDestroyed \r\n        } \r\n    })\r\n    public targetWeapons: Weapon[] = [];\r\n\r\n    @property({ type: Emitter, displayName: \"发射器\" })\r\n    public emitter: Emitter | null = null; // 武器发射器\r\n\r\n    private m_state: eWeaponState = eWeaponState.None;\r\n    private m_stateElapsedTime: number = 0; // 当前状态持续时间\r\n    private m_target: Entity | null = null; // 武器目标\r\n    private m_owner: Entity | null = null;  // 武器归属\r\n\r\n    init() {\r\n        this.m_state = eWeaponState.None;\r\n        if (this.emitter) {\r\n            this.emitter.changeStatus(eEmitterStatus.None);\r\n        }\r\n    }\r\n\r\n    public get state(): eWeaponState {\r\n        return this.m_state;\r\n    }\r\n\r\n    public set state(value: eWeaponState) {\r\n        if (this.m_state === value) {\r\n            return;\r\n        }\r\n\r\n        this.m_state = value;\r\n        this.m_stateElapsedTime = 0;\r\n    }\r\n\r\n    public setOwner(owner: Entity): Weapon {\r\n        this.m_owner = owner;\r\n        return this;\r\n    }\r\n\r\n    public setTarget(target: Entity | null): Weapon {\r\n        this.m_target = target;\r\n        if (this.useCondition === eWeaponUseCond.Immediate) {\r\n            this.activate();\r\n        }\r\n\r\n        return this;\r\n    }\r\n\r\n    public tick(dt: number) {\r\n        this.m_stateElapsedTime += dt;\r\n        switch (this.m_state) {\r\n            case eWeaponState.Aiming:\r\n                this.tickAiming(dt);\r\n                break;\r\n            case eWeaponState.Emitting:\r\n                this.tickEmitting(dt);\r\n                break;\r\n            case eWeaponState.None:\r\n\r\n                break;\r\n        }\r\n    }\r\n\r\n    // 激活武器\r\n    private activate() {\r\n        this.state = this.aimingTime > 0 ? eWeaponState.Aiming : eWeaponState.Emitting;\r\n    }\r\n\r\n    private tickAiming(dt: number) {\r\n        if (this.m_stateElapsedTime >= this.aimingTime) {\r\n            this.state = eWeaponState.Emitting;\r\n        }\r\n\r\n        this.turnToTarget(dt);\r\n    }\r\n\r\n    private tickEmitting(dt: number) {\r\n        if (this.emitter && this.emitter.status !== eEmitterStatus.Emitting) {\r\n            this.emitter.changeStatus(eEmitterStatus.Emitting);\r\n        }\r\n    }\r\n\r\n    private tickNone(dt: number) {\r\n        if (this.useCondition === eWeaponUseCond.WeaponDestroyed) {\r\n            for (let weapon of this.targetWeapons) {\r\n                if (!weapon.isDead) {\r\n                    return;\r\n                }\r\n            }\r\n            this.activate();\r\n        }\r\n    }\r\n\r\n    private turnToTarget(dt: number) {\r\n        if (!this.m_target || !this.m_owner) {\r\n            return;\r\n        }\r\n        const ownerNode = this.m_owner.node;\r\n        const targetNode = this.m_target.node;\r\n        const angle = ownerNode.angle;\r\n        const targetAngle = Math.atan2(targetNode.worldPosition.y - ownerNode.worldPosition.y, targetNode.worldPosition.x - ownerNode.worldPosition.x) * 180 / Math.PI - 90;\r\n        let deltaAngle = targetAngle - angle;\r\n        if (deltaAngle > 180) {\r\n            deltaAngle -= 360;\r\n        } else if (deltaAngle < -180) {\r\n            deltaAngle += 360;\r\n        }\r\n        const maxDelta = this.turnSpeed * dt / 1000;\r\n        if (Math.abs(deltaAngle) <= maxDelta) {\r\n            ownerNode.angle = targetAngle;\r\n        }\r\n        else {\r\n            ownerNode.angle += Math.sign(deltaAngle) * maxDelta;\r\n        }\r\n    }\r\n}"]}