12:01:37.398 debug: 2025/9/22 12:01:37
12:01:37.398 debug: Project: E:\M2Game\Client
12:01:37.398 debug: Targets: editor,preview
12:01:37.418 debug: Incremental file seems great.
12:01:37.419 debug: Engine path: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine
12:01:37.426 debug: Initializing target [Editor]
12:01:37.426 debug: Loading cache
12:01:37.434 debug: Loading cache costs 7.831299999999828ms.
12:01:37.434 debug: Engine features shipped in editor: base,gfx-webgl,gfx-webgl2,gfx-empty,gfx-webgpu,3d,animation,skeletal-animation,2d,rich-text,mask,graphics,ui-skew,ui,affine-transform,particle,particle-2d,physics-framework,physics-cannon,physics-physx,physics-ammo,physics-builtin,physics-2d-framework,physics-2d-box2d-jsb,physics-2d-box2d,physics-2d-builtin,physics-2d-box2d-wasm,intersection-2d,primitive,profiler,occlusion-query,geometry-renderer,debug-renderer,audio,video,xr,light-probe,terrain,webview,tween,tiled-map,vendor-google,spine-3.8,spine-4.2,dragon-bones,marionette,procedural-animation,custom-pipeline,custom-pipeline-builtin-scripts,custom-pipeline-post-process,legacy-pipeline,websocket,websocket-server,meshopt
12:01:37.435 debug: Our import map(foo:/bar): {
  "imports": {
    "cc": "cce:/internal/x/cc"
  }
}
12:01:37.435 debug: Initializing target [Preview]
12:01:37.435 debug: Loading cache
12:01:37.441 debug: Loading cache costs 6.401400000000194ms.
12:01:37.441 debug: Our import map(foo:/bar): {
  "imports": {
    "cc": "cce:/internal/x/cc"
  }
}
12:01:37.471 debug: Sync engine features: 2d,3d,affine-transform,animation,audio,base,custom-pipeline,debug-renderer,gfx-webgl,gfx-webgl2,graphics,intersection-2d,marionette,mask,meshopt,particle-2d,physics-2d-builtin,profiler,rich-text,skeletal-animation,spine-3.8,tween,ui,video,websocket,webview,custom-pipeline
12:01:37.473 debug: Reset databases. Enumerated domains: [
  {
    "root": "db://internal/",
    "physical": "C:\\ProgramData\\cocos\\editors\\Creator\\3.8.6\\resources\\resources\\3d\\engine\\editor\\assets"
  },
  {
    "root": "db://assets/",
    "physical": "E:\\M2Game\\Client\\assets"
  }
]
12:01:37.473 debug: Our import map(foo:/bar): {
  "imports": {
    "cc": "cce:/internal/x/cc",
    "db://internal/": "file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/",
    "db://assets/": "file:///E:/M2Game/Client/assets/"
  }
}
12:01:37.474 debug: Our import map(foo:/bar): {
  "imports": {
    "cc": "cce:/internal/x/cc",
    "db://internal/": "file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/",
    "db://assets/": "file:///E:/M2Game/Client/assets/"
  }
}
12:01:37.474 debug: Pulling asset-db.
12:01:37.512 debug: Fetch asset-db cost: 37.66389999999956ms.
12:01:37.513 debug: Build iteration starts.
Number of accumulated asset changes: 245
Feature changed: false
12:01:37.513 debug: Target(editor) build started.
12:01:37.515 debug: Detected change: cce:/internal/x/cc. Last mtime: Thu Jan 01 1970 08:00:04 GMT+0800 (中国标准时间), Current mtime: Thu Jan 01 1970 08:00:04 GMT+0800 (中国标准时间)
