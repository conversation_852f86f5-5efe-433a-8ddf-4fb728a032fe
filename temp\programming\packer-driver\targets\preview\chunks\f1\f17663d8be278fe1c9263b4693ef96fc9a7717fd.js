System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Vec2, <PERSON><PERSON>, CCInteger, ExpressionValue, eCompareOp, eConditionOp, eTargetValueType, eWrapMode, eEasing, EnemyEnum, _dec, _dec2, _dec3, _dec4, _dec5, _dec6, _dec7, _class, _class2, _descriptor, _descriptor2, _descriptor3, _descriptor4, _dec8, _dec9, _dec10, _dec11, _dec12, _dec13, _dec14, _dec15, _dec16, _dec17, _dec18, _dec19, _dec20, _dec21, _dec22, _class4, _class5, _descriptor5, _descriptor6, _descriptor7, _descriptor8, _descriptor9, _descriptor10, _descriptor11, _descriptor12, _descriptor13, _dec23, _dec24, _dec25, _dec26, _dec27, _class7, _class8, _descriptor14, _descriptor15, _descriptor16, _descriptor17, _dec28, _dec29, _dec30, _dec31, _class10, _class11, _descriptor18, _descriptor19, _descriptor20, _dec32, _dec33, _dec34, _dec35, _class13, _class14, _descriptor21, _descriptor22, _descriptor23, _dec36, _dec37, _class16, _class17, _descriptor24, _dec38, _dec39, _dec40, _dec41, _dec42, _dec43, _dec44, _dec45, _dec46, _dec47, _dec48, _dec49, _dec50, _dec51, _dec52, _class19, _class20, _descriptor25, _descriptor26, _descriptor27, _descriptor28, _descriptor29, _descriptor30, _descriptor31, _descriptor32, _descriptor33, _crd, ccclass, property, eSpawnOrder, eWaveAngleType, eWaveCompletion, eWaveConditionType, eWaveActionType, eWaveConditionTypeCn, eWaveActionTypeCn, WaveConditionData, WaveActionData, WaveEventGroupData, SpawnGroup, FormationPoint, FormationGroup, WaveData;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfExpressionValue(extras) {
    _reporterNs.report("ExpressionValue", "./bullet/ExpressionValue", _context.meta, extras);
  }

  function _reportPossibleCrUseOfIEventGroupData(extras) {
    _reporterNs.report("IEventGroupData", "db://assets/bundles/common/script/game/eventgroup/IEventGroup", _context.meta, extras);
  }

  function _reportPossibleCrUseOfIEventConditionData(extras) {
    _reporterNs.report("IEventConditionData", "./bullet/EventGroupData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfeCompareOp(extras) {
    _reporterNs.report("eCompareOp", "./bullet/EventGroupData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfeConditionOp(extras) {
    _reporterNs.report("eConditionOp", "./bullet/EventGroupData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfIEventActionData(extras) {
    _reporterNs.report("IEventActionData", "./bullet/EventGroupData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfeTargetValueType(extras) {
    _reporterNs.report("eTargetValueType", "./bullet/EventGroupData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfeWrapMode(extras) {
    _reporterNs.report("eWrapMode", "./bullet/EventGroupData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfeEasing(extras) {
    _reporterNs.report("eEasing", "../eventgroup/Easing", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEnemyEnum(extras) {
    _reporterNs.report("EnemyEnum", "db://assets/editor/enum-gen/EnemyEnum", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Vec2 = _cc.Vec2;
      Enum = _cc.Enum;
      CCInteger = _cc.CCInteger;
    }, function (_unresolved_2) {
      ExpressionValue = _unresolved_2.ExpressionValue;
    }, function (_unresolved_3) {
      eCompareOp = _unresolved_3.eCompareOp;
      eConditionOp = _unresolved_3.eConditionOp;
      eTargetValueType = _unresolved_3.eTargetValueType;
      eWrapMode = _unresolved_3.eWrapMode;
    }, function (_unresolved_4) {
      eEasing = _unresolved_4.eEasing;
    }, function (_unresolved_5) {
      EnemyEnum = _unresolved_5.EnemyEnum;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "196ac9cYZJPB69dYqcYeFzP", "WaveData", undefined);

      __checkObsolete__(['_decorator', 'error', 'v2', 'Vec2', 'Prefab', 'Enum', 'CCInteger', 'CCFloat']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("eSpawnOrder", eSpawnOrder = /*#__PURE__*/function (eSpawnOrder) {
        eSpawnOrder[eSpawnOrder["Sequential"] = 0] = "Sequential";
        eSpawnOrder[eSpawnOrder["Random"] = 1] = "Random";
        return eSpawnOrder;
      }({}));

      _export("eWaveAngleType", eWaveAngleType = /*#__PURE__*/function (eWaveAngleType) {
        eWaveAngleType[eWaveAngleType["FacingMoveDir"] = 0] = "FacingMoveDir";
        eWaveAngleType[eWaveAngleType["FacingPlayer"] = 1] = "FacingPlayer";
        eWaveAngleType[eWaveAngleType["Fixed"] = 2] = "Fixed";
        return eWaveAngleType;
      }({})); // 波次完成条件


      _export("eWaveCompletion", eWaveCompletion = /*#__PURE__*/function (eWaveCompletion) {
        eWaveCompletion[eWaveCompletion["Time"] = 0] = "Time";
        eWaveCompletion[eWaveCompletion["SpawnCount"] = 1] = "SpawnCount";
        return eWaveCompletion;
      }({}));

      _export("eWaveConditionType", eWaveConditionType = /*#__PURE__*/function (eWaveConditionType) {
        eWaveConditionType[eWaveConditionType["Player_Level"] = 0] = "Player_Level";
        eWaveConditionType[eWaveConditionType["Spawn_Count"] = 1] = "Spawn_Count";
        return eWaveConditionType;
      }({}));

      _export("eWaveActionType", eWaveActionType = /*#__PURE__*/function (eWaveActionType) {
        eWaveActionType[eWaveActionType["Spawn_Interval"] = 0] = "Spawn_Interval";
        eWaveActionType[eWaveActionType["Spawn_Angle"] = 1] = "Spawn_Angle";
        return eWaveActionType;
      }({}));

      _export("eWaveConditionTypeCn", eWaveConditionTypeCn = /*#__PURE__*/function (eWaveConditionTypeCn) {
        eWaveConditionTypeCn[eWaveConditionTypeCn["\u73A9\u5BB6\u7B49\u7EA7"] = 0] = "\u73A9\u5BB6\u7B49\u7EA7";
        eWaveConditionTypeCn[eWaveConditionTypeCn["\u5F53\u524D\u6CE2\u6B21\u751F\u6210\u6570\u91CF"] = 1] = "\u5F53\u524D\u6CE2\u6B21\u751F\u6210\u6570\u91CF";
        return eWaveConditionTypeCn;
      }({}));

      _export("eWaveActionTypeCn", eWaveActionTypeCn = /*#__PURE__*/function (eWaveActionTypeCn) {
        eWaveActionTypeCn[eWaveActionTypeCn["\u51FA\u751F\u95F4\u9694"] = 0] = "\u51FA\u751F\u95F4\u9694";
        eWaveActionTypeCn[eWaveActionTypeCn["\u51FA\u751F\u89D2\u5EA6"] = 1] = "\u51FA\u751F\u89D2\u5EA6";
        return eWaveActionTypeCn;
      }({})); // 和发射器的事件组类似


      _export("WaveConditionData", WaveConditionData = (_dec = ccclass("WaveConditionData"), _dec2 = property({
        type: Enum(_crd && eConditionOp === void 0 ? (_reportPossibleCrUseOfeConditionOp({
          error: Error()
        }), eConditionOp) : eConditionOp),
        displayName: '条件关系'
      }), _dec3 = property({
        visible: false
      }), _dec4 = property({
        type: Enum(eWaveConditionTypeCn),
        displayName: '条件类型'
      }), _dec5 = property({
        type: Enum(_crd && eCompareOp === void 0 ? (_reportPossibleCrUseOfeCompareOp({
          error: Error()
        }), eCompareOp) : eCompareOp),
        displayName: '比较方式'
      }), _dec6 = property({
        visible: false
      }), _dec7 = property({
        displayName: '目标值'
      }), _dec(_class = (_class2 = class WaveConditionData {
        constructor() {
          _initializerDefineProperty(this, "op", _descriptor, this);

          _initializerDefineProperty(this, "type", _descriptor2, this);

          _initializerDefineProperty(this, "compareOp", _descriptor3, this);

          // 条件值: 例如持续时间、距离
          _initializerDefineProperty(this, "targetValue", _descriptor4, this);
        }

        get typeCn() {
          return this.type;
        }

        set typeCn(value) {
          this.type = value;
        }

        get targetValueStr() {
          return this.targetValue.raw;
        }

        set targetValueStr(value) {
          this.targetValue.raw = value;
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "op", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return (_crd && eConditionOp === void 0 ? (_reportPossibleCrUseOfeConditionOp({
            error: Error()
          }), eConditionOp) : eConditionOp).And;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "type", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return eWaveConditionType.Player_Level;
        }
      }), _applyDecoratedDescriptor(_class2.prototype, "typeCn", [_dec4], Object.getOwnPropertyDescriptor(_class2.prototype, "typeCn"), _class2.prototype), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "compareOp", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return (_crd && eCompareOp === void 0 ? (_reportPossibleCrUseOfeCompareOp({
            error: Error()
          }), eCompareOp) : eCompareOp).Equal;
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class2.prototype, "targetValue", [_dec6], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return new (_crd && ExpressionValue === void 0 ? (_reportPossibleCrUseOfExpressionValue({
            error: Error()
          }), ExpressionValue) : ExpressionValue)('0');
        }
      }), _applyDecoratedDescriptor(_class2.prototype, "targetValueStr", [_dec7], Object.getOwnPropertyDescriptor(_class2.prototype, "targetValueStr"), _class2.prototype)), _class2)) || _class));

      _export("WaveActionData", WaveActionData = (_dec8 = ccclass("WaveActionData"), _dec9 = property({
        displayName: '行为名称(编辑器下调试用)',
        editorOnly: true
      }), _dec10 = property({
        visible: false
      }), _dec11 = property({
        type: Enum(eWaveActionTypeCn),
        displayName: '行为类型'
      }), _dec12 = property({
        visible: false
      }), _dec13 = property({
        displayName: '延迟时间',
        tooltip: '事件触发后，延迟多少毫秒开始执行该行为'
      }), _dec14 = property({
        visible: false
      }), _dec15 = property({
        displayName: '持续时间'
      }), _dec16 = property({
        visible: false
      }), _dec17 = property({
        displayName: '目标值'
      }), _dec18 = property({
        type: Enum(_crd && eTargetValueType === void 0 ? (_reportPossibleCrUseOfeTargetValueType({
          error: Error()
        }), eTargetValueType) : eTargetValueType),
        displayName: '目标值类型'
      }), _dec19 = property({
        visible: false
      }), _dec20 = property({
        displayName: '变换到目标值所需时间'
      }), _dec21 = property({
        type: Enum(_crd && eWrapMode === void 0 ? (_reportPossibleCrUseOfeWrapMode({
          error: Error()
        }), eWrapMode) : eWrapMode),
        displayName: '循环模式'
      }), _dec22 = property({
        type: Enum(_crd && eEasing === void 0 ? (_reportPossibleCrUseOfeEasing({
          error: Error()
        }), eEasing) : eEasing),
        displayName: '缓动函数'
      }), _dec8(_class4 = (_class5 = class WaveActionData {
        constructor() {
          _initializerDefineProperty(this, "name", _descriptor5, this);

          _initializerDefineProperty(this, "type", _descriptor6, this);

          _initializerDefineProperty(this, "delay", _descriptor7, this);

          // 持续时间: 0表示立即执行
          _initializerDefineProperty(this, "duration", _descriptor8, this);

          _initializerDefineProperty(this, "targetValue", _descriptor9, this);

          _initializerDefineProperty(this, "targetValueType", _descriptor10, this);

          _initializerDefineProperty(this, "transitionDuration", _descriptor11, this);

          _initializerDefineProperty(this, "wrapMode", _descriptor12, this);

          _initializerDefineProperty(this, "easing", _descriptor13, this);
        }

        get typeCn() {
          return this.type;
        }

        set typeCn(value) {
          this.type = value;
        }

        get delayStr() {
          return this.delay.value;
        }

        set delayStr(value) {
          this.delay.value = value;
        }

        get durationStr() {
          return this.duration.raw;
        }

        set durationStr(value) {
          this.duration.raw = value;
        }

        get targetValueStr() {
          return this.targetValue.raw;
        }

        set targetValueStr(value) {
          this.targetValue.raw = value;
        }

        get transitionDurationStr() {
          return this.transitionDuration.value;
        }

        set transitionDurationStr(value) {
          this.transitionDuration.value = value;
        }

      }, (_descriptor5 = _applyDecoratedDescriptor(_class5.prototype, "name", [_dec9], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return "";
        }
      }), _descriptor6 = _applyDecoratedDescriptor(_class5.prototype, "type", [_dec10], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return eWaveActionType.Spawn_Interval;
        }
      }), _applyDecoratedDescriptor(_class5.prototype, "typeCn", [_dec11], Object.getOwnPropertyDescriptor(_class5.prototype, "typeCn"), _class5.prototype), _descriptor7 = _applyDecoratedDescriptor(_class5.prototype, "delay", [_dec12], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return new (_crd && ExpressionValue === void 0 ? (_reportPossibleCrUseOfExpressionValue({
            error: Error()
          }), ExpressionValue) : ExpressionValue)('0');
        }
      }), _applyDecoratedDescriptor(_class5.prototype, "delayStr", [_dec13], Object.getOwnPropertyDescriptor(_class5.prototype, "delayStr"), _class5.prototype), _descriptor8 = _applyDecoratedDescriptor(_class5.prototype, "duration", [_dec14], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return new (_crd && ExpressionValue === void 0 ? (_reportPossibleCrUseOfExpressionValue({
            error: Error()
          }), ExpressionValue) : ExpressionValue)('0');
        }
      }), _applyDecoratedDescriptor(_class5.prototype, "durationStr", [_dec15], Object.getOwnPropertyDescriptor(_class5.prototype, "durationStr"), _class5.prototype), _descriptor9 = _applyDecoratedDescriptor(_class5.prototype, "targetValue", [_dec16], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return new (_crd && ExpressionValue === void 0 ? (_reportPossibleCrUseOfExpressionValue({
            error: Error()
          }), ExpressionValue) : ExpressionValue)('0');
        }
      }), _applyDecoratedDescriptor(_class5.prototype, "targetValueStr", [_dec17], Object.getOwnPropertyDescriptor(_class5.prototype, "targetValueStr"), _class5.prototype), _descriptor10 = _applyDecoratedDescriptor(_class5.prototype, "targetValueType", [_dec18], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return (_crd && eTargetValueType === void 0 ? (_reportPossibleCrUseOfeTargetValueType({
            error: Error()
          }), eTargetValueType) : eTargetValueType).Absolute;
        }
      }), _descriptor11 = _applyDecoratedDescriptor(_class5.prototype, "transitionDuration", [_dec19], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return new (_crd && ExpressionValue === void 0 ? (_reportPossibleCrUseOfExpressionValue({
            error: Error()
          }), ExpressionValue) : ExpressionValue)('0');
        }
      }), _applyDecoratedDescriptor(_class5.prototype, "transitionDurationStr", [_dec20], Object.getOwnPropertyDescriptor(_class5.prototype, "transitionDurationStr"), _class5.prototype), _descriptor12 = _applyDecoratedDescriptor(_class5.prototype, "wrapMode", [_dec21], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return (_crd && eWrapMode === void 0 ? (_reportPossibleCrUseOfeWrapMode({
            error: Error()
          }), eWrapMode) : eWrapMode).Once;
        }
      }), _descriptor13 = _applyDecoratedDescriptor(_class5.prototype, "easing", [_dec22], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return (_crd && eEasing === void 0 ? (_reportPossibleCrUseOfeEasing({
            error: Error()
          }), eEasing) : eEasing).Linear;
        }
      })), _class5)) || _class4));

      _export("WaveEventGroupData", WaveEventGroupData = (_dec23 = ccclass("WaveEventGroupData"), _dec24 = property({
        displayName: '事件组名称'
      }), _dec25 = property({
        displayName: '触发次数',
        visible: false
      }), _dec26 = property({
        type: [WaveConditionData],
        displayName: '条件列表'
      }), _dec27 = property({
        type: [WaveActionData],
        displayName: '行为列表'
      }), _dec23(_class7 = (_class8 = class WaveEventGroupData {
        constructor() {
          _initializerDefineProperty(this, "name", _descriptor14, this);

          _initializerDefineProperty(this, "triggerCount", _descriptor15, this);

          _initializerDefineProperty(this, "conditions", _descriptor16, this);

          _initializerDefineProperty(this, "actions", _descriptor17, this);
        }

      }, (_descriptor14 = _applyDecoratedDescriptor(_class8.prototype, "name", [_dec24], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return "";
        }
      }), _descriptor15 = _applyDecoratedDescriptor(_class8.prototype, "triggerCount", [_dec25], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 1;
        }
      }), _descriptor16 = _applyDecoratedDescriptor(_class8.prototype, "conditions", [_dec26], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return [];
        }
      }), _descriptor17 = _applyDecoratedDescriptor(_class8.prototype, "actions", [_dec27], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return [];
        }
      })), _class8)) || _class7));

      _export("SpawnGroup", SpawnGroup = (_dec28 = ccclass("SpawnGroup"), _dec29 = property({
        type: Enum(_crd && EnemyEnum === void 0 ? (_reportPossibleCrUseOfEnemyEnum({
          error: Error()
        }), EnemyEnum) : EnemyEnum),
        displayName: "飞机ID"
      }), _dec30 = property({
        type: CCInteger,
        displayName: "权重"
      }), _dec31 = property({
        visible: false,
        serializable: false
      }), _dec28(_class10 = (_class11 = class SpawnGroup {
        constructor() {
          _initializerDefineProperty(this, "planeID", _descriptor18, this);

          _initializerDefineProperty(this, "weight", _descriptor19, this);

          _initializerDefineProperty(this, "selfWeight", _descriptor20, this);
        }

      }, (_descriptor18 = _applyDecoratedDescriptor(_class11.prototype, "planeID", [_dec29], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 0;
        }
      }), _descriptor19 = _applyDecoratedDescriptor(_class11.prototype, "weight", [_dec30], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 50;
        }
      }), _descriptor20 = _applyDecoratedDescriptor(_class11.prototype, "selfWeight", [_dec31], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 0;
        }
      })), _class11)) || _class10)); // 阵型点


      _export("FormationPoint", FormationPoint = (_dec32 = ccclass("FormationPoint"), _dec33 = property({
        type: CCInteger,
        displayName: "X坐标"
      }), _dec34 = property({
        type: CCInteger,
        displayName: "Y坐标"
      }), _dec35 = property({
        type: [SpawnGroup],
        displayName: "出生组"
      }), _dec32(_class13 = (_class14 = class FormationPoint {
        constructor() {
          _initializerDefineProperty(this, "x", _descriptor21, this);

          _initializerDefineProperty(this, "y", _descriptor22, this);

          // 这个点可能出生的单位
          _initializerDefineProperty(this, "spawnGroup", _descriptor23, this);
        }

      }, (_descriptor21 = _applyDecoratedDescriptor(_class14.prototype, "x", [_dec33], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 0;
        }
      }), _descriptor22 = _applyDecoratedDescriptor(_class14.prototype, "y", [_dec34], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 0;
        }
      }), _descriptor23 = _applyDecoratedDescriptor(_class14.prototype, "spawnGroup", [_dec35], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return [];
        }
      })), _class14)) || _class13)); // 阵型组


      _export("FormationGroup", FormationGroup = (_dec36 = ccclass("FormationGroup"), _dec37 = property({
        type: [FormationPoint],
        displayName: '阵型点'
      }), _dec36(_class16 = (_class17 = class FormationGroup {
        constructor() {
          _initializerDefineProperty(this, "points", _descriptor24, this);
        }

      }, (_descriptor24 = _applyDecoratedDescriptor(_class17.prototype, "points", [_dec37], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return [];
        }
      })), _class17)) || _class16));
      /**
       * 波次数据：未来代替现有的EnemyWave
       * 所有时间相关的，单位都是毫秒(ms)
       */


      _export("WaveData", WaveData = (_dec38 = ccclass("WaveData"), _dec39 = property({
        type: [SpawnGroup],
        displayName: "出生组"
      }), _dec40 = property({
        type: Enum(eSpawnOrder),
        displayName: "出生顺序"
      }), _dec41 = property({
        type: Enum(eWaveCompletion),
        displayName: "波次完成条件"
      }), _dec42 = property({
        visible: false
      }), _dec43 = property({
        displayName: "完成条件参数"
      }), _dec44 = property({
        visible: false
      }), _dec45 = property({
        displayName: "出生间隔(ms)"
      }), _dec46 = property({
        visible: false
      }), _dec47 = property({
        visible: false
      }), _dec48 = property({
        displayName: "出生位置X"
      }), _dec49 = property({
        displayName: "出生位置Y"
      }), _dec50 = property({
        visible: false
      }), _dec51 = property({
        displayName: "出生角度"
      }), _dec52 = property({
        type: [WaveEventGroupData],
        displayName: '事件组'
      }), _dec38(_class19 = (_class20 = class WaveData {
        constructor() {
          // 波次都由LevelTrigger来触发，例如: 上一波结束后触发，或者到达某个距离后触发
          // 因此这里不再配置触发条件
          _initializerDefineProperty(this, "spawnGroup", _descriptor25, this);

          _initializerDefineProperty(this, "spawnOrder", _descriptor26, this);

          // @property({visible:false})
          // public count : ExpressionValue = new ExpressionValue('5');
          // @property({displayName: "数量"})
          // public get countStr(): string { return this.count.raw; }
          // public set countStr(value: string) { this.count.raw = value; }
          _initializerDefineProperty(this, "waveCompletion", _descriptor27, this);

          _initializerDefineProperty(this, "waveCompletionParam", _descriptor28, this);

          _initializerDefineProperty(this, "spawnInterval", _descriptor29, this);

          _initializerDefineProperty(this, "spawnPosX", _descriptor30, this);

          _initializerDefineProperty(this, "spawnPosY", _descriptor31, this);

          this._spawnPos = new Vec2();

          _initializerDefineProperty(this, "spawnAngle", _descriptor32, this);

          // 以下几个属性走怪物的配置里。
          // @property({visible:false})
          // public spawnSpeed: ExpressionValue = new ExpressionValue('500');
          // @property({displayName: "出生速度"})
          // public get spawnSpeedStr(): string { return this.spawnSpeed.raw; }
          // public set spawnSpeedStr(value: string) { this.spawnSpeed.raw = value; }
          // @property({type: Enum(eWaveAngleType), displayName: "单位朝向类型"})
          // public planeAngleType: eWaveAngleType = eWaveAngleType.FacingMoveDir;
          // public get isFixedAngleType(): boolean {
          //     return this.planeAngleType == eWaveAngleType.Fixed;
          // }
          // @property({type: CCInteger, displayName: "单位朝向", tooltip: '仅在单位朝向类型为Fixed时有效', 
          //     visible() { 
          //         //@ts-ignore
          //         return this.planeAngleType == eWaveAngleType.Fixed;
          //     }
          // })
          // public planeAngleFixed: number = 0;
          // @property({type: CCInteger, displayName: "单位延迟销毁", tooltip: '单位离开屏幕后, 延迟销毁的时间(ms), -1表示不销毁'})
          // public delayDestroy: number = 5000;
          _initializerDefineProperty(this, "eventGroupData", _descriptor33, this);
        }

        get waveCompletionParamStr() {
          return this.waveCompletionParam.raw;
        }

        set waveCompletionParamStr(value) {
          this.waveCompletionParam.raw = value;
        }

        get spawnIntervalStr() {
          return this.spawnInterval.raw;
        }

        set spawnIntervalStr(value) {
          this.spawnInterval.raw = value;
        }

        get spawnPosXStr() {
          return this.spawnPosX.raw;
        }

        set spawnPosXStr(value) {
          this.spawnPosX.raw = value;
        }

        get spawnPosYStr() {
          return this.spawnPosY.raw;
        }

        set spawnPosYStr(value) {
          this.spawnPosY.raw = value;
        }

        get spawnPos() {
          this._spawnPos.set(this.spawnPosX.eval(), this.spawnPosY.eval());

          return this._spawnPos;
        }

        get spawnAngleStr() {
          return this.spawnAngle.raw;
        }

        set spawnAngleStr(value) {
          this.spawnAngle.raw = value;
        }

      }, (_descriptor25 = _applyDecoratedDescriptor(_class20.prototype, "spawnGroup", [_dec39], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return [];
        }
      }), _descriptor26 = _applyDecoratedDescriptor(_class20.prototype, "spawnOrder", [_dec40], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return eSpawnOrder.Random;
        }
      }), _descriptor27 = _applyDecoratedDescriptor(_class20.prototype, "waveCompletion", [_dec41], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return eWaveCompletion.SpawnCount;
        }
      }), _descriptor28 = _applyDecoratedDescriptor(_class20.prototype, "waveCompletionParam", [_dec42], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return new (_crd && ExpressionValue === void 0 ? (_reportPossibleCrUseOfExpressionValue({
            error: Error()
          }), ExpressionValue) : ExpressionValue)('5');
        }
      }), _applyDecoratedDescriptor(_class20.prototype, "waveCompletionParamStr", [_dec43], Object.getOwnPropertyDescriptor(_class20.prototype, "waveCompletionParamStr"), _class20.prototype), _descriptor29 = _applyDecoratedDescriptor(_class20.prototype, "spawnInterval", [_dec44], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return new (_crd && ExpressionValue === void 0 ? (_reportPossibleCrUseOfExpressionValue({
            error: Error()
          }), ExpressionValue) : ExpressionValue)('1000');
        }
      }), _applyDecoratedDescriptor(_class20.prototype, "spawnIntervalStr", [_dec45], Object.getOwnPropertyDescriptor(_class20.prototype, "spawnIntervalStr"), _class20.prototype), _descriptor30 = _applyDecoratedDescriptor(_class20.prototype, "spawnPosX", [_dec46], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return new (_crd && ExpressionValue === void 0 ? (_reportPossibleCrUseOfExpressionValue({
            error: Error()
          }), ExpressionValue) : ExpressionValue)('0');
        }
      }), _descriptor31 = _applyDecoratedDescriptor(_class20.prototype, "spawnPosY", [_dec47], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return new (_crd && ExpressionValue === void 0 ? (_reportPossibleCrUseOfExpressionValue({
            error: Error()
          }), ExpressionValue) : ExpressionValue)('0');
        }
      }), _applyDecoratedDescriptor(_class20.prototype, "spawnPosXStr", [_dec48], Object.getOwnPropertyDescriptor(_class20.prototype, "spawnPosXStr"), _class20.prototype), _applyDecoratedDescriptor(_class20.prototype, "spawnPosYStr", [_dec49], Object.getOwnPropertyDescriptor(_class20.prototype, "spawnPosYStr"), _class20.prototype), _descriptor32 = _applyDecoratedDescriptor(_class20.prototype, "spawnAngle", [_dec50], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return new (_crd && ExpressionValue === void 0 ? (_reportPossibleCrUseOfExpressionValue({
            error: Error()
          }), ExpressionValue) : ExpressionValue)('270');
        }
      }), _applyDecoratedDescriptor(_class20.prototype, "spawnAngleStr", [_dec51], Object.getOwnPropertyDescriptor(_class20.prototype, "spawnAngleStr"), _class20.prototype), _descriptor33 = _applyDecoratedDescriptor(_class20.prototype, "eventGroupData", [_dec52], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return [];
        }
      })), _class20)) || _class19));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=f17663d8be278fe1c9263b4693ef96fc9a7717fd.js.map