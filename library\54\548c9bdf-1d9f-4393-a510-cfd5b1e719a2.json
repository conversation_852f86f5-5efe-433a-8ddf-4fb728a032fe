[{"__type__": "cc.Prefab", "_name": "RandomElement_Clouds", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "persistent": false}, {"__type__": "cc.Node", "_name": "RandomElement_Clouds", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 5}, {"__id__": 8}, {"__id__": 11}], "_active": true, "_components": [{"__id__": 14}, {"__id__": 16}], "_prefab": {"__id__": 22}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 1}, "_prefab": {"__id__": 3}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 2}, "asset": {"__uuid__": "3b0ac7d3-b3e7-4ef7-9910-2eaaf0f37d74", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 4}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "4697dlZ7pCh7wtDJ1mMqyb", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 1}, "_prefab": {"__id__": 6}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 5}, "asset": {"__uuid__": "a394c047-8806-4d84-b5a2-ad382eeedc4e", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 7}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "d3ERF7jH1Ptq/nR/XLV73d", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 1}, "_prefab": {"__id__": 9}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 8}, "asset": {"__uuid__": "e01e576b-d981-4aa2-afa4-e22cdd8d35e9", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 10}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "45jWxdY+JPQ4LcIO+mv8ln", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 1}, "_prefab": {"__id__": 12}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 11}, "asset": {"__uuid__": "a23b329a-d6f4-4883-ab94-677abc51e3fd", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 13}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "42jp/FZyVMGKKnft1a1E88", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 15}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "48ODoXnJ9J3b58WDD6RtLO"}, {"__type__": "c06femD0WhPk63lQ/D1YJtE", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 17}, "terrain": [{"__id__": 18}, {"__id__": 19}, {"__id__": 20}, {"__id__": 21}], "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4d/a+smUtPeZ18iQ2ULM15"}, {"__type__": "TerrainElem", "weight": 90, "elem": {"__uuid__": "3b0ac7d3-b3e7-4ef7-9910-2eaaf0f37d74", "__expectedType__": "cc.Prefab"}, "offSet": {"__type__": "cc.Vec2", "x": 0, "y": 0}}, {"__type__": "TerrainElem", "weight": 80, "elem": {"__uuid__": "a394c047-8806-4d84-b5a2-ad382eeedc4e", "__expectedType__": "cc.Prefab"}, "offSet": {"__type__": "cc.Vec2", "x": 0, "y": 0}}, {"__type__": "TerrainElem", "weight": 30, "elem": {"__uuid__": "e01e576b-d981-4aa2-afa4-e22cdd8d35e9", "__expectedType__": "cc.Prefab"}, "offSet": {"__type__": "cc.Vec2", "x": 0, "y": 0}}, {"__type__": "TerrainElem", "weight": 20, "elem": {"__uuid__": "a23b329a-d6f4-4883-ab94-677abc51e3fd", "__expectedType__": "cc.Prefab"}, "offSet": {"__type__": "cc.Vec2", "x": 0, "y": 0}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "56xTtHP4tJg4s0D/yyljUz", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": [{"__id__": 11}, {"__id__": 8}, {"__id__": 5}, {"__id__": 2}]}]