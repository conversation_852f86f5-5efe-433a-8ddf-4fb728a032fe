"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.onCreateMenu = onCreateMenu;
exports.onAssetMenu = onAssetMenu;
function onCreateMenu(assetInfo) {
    return [
        {
            label: '飞机',
            submenu: [
                {
                    label: '创建波次',
                    click() {
                        console.log('wave');
                        console.log(assetInfo);
                    },
                },
                {
                    label: '创建阵型',
                    click() {
                        console.log('formation');
                        console.log(assetInfo);
                    },
                },
            ],
        },
    ];
}
;
function onAssetMenu(assetInfo) {
    return [
        {
            label: '飞机',
            submenu: [
                {
                    label: '创建关卡Prefab',
                    enabled: assetInfo.isDirectory,
                    click() {
                        console.log('get it');
                        console.log(assetInfo);
                    },
                },
                {
                    label: '创建子弹prefab',
                    enabled: !assetInfo.isDirectory,
                    click() {
                        console.log('yes, you clicked');
                        console.log(assetInfo);
                    },
                },
            ],
        },
    ];
}
;
//# sourceMappingURL=data:application/json;base64,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