// // @ts-ignore
// import packageJSON from '../package.json';
import { MenuAssetInfo, IAssetInfo } from '@cocos/creator-types/editor/packages/package-asset/@types/public';
import { IDialogAction } from '@cocos/creator-types/editor/packages/package-asset/@types/public/dialog';

export function onCreateMenu(assetInfo: MenuAssetInfo) {
  return [
    {
      label: '飞机游戏',
      submenu: [
        {
          label: '创建波次',
          click() {
            console.log('wave');
            console.log(assetInfo);
          },
        },
        {
          label: '创建阵型',
          click() {
            console.log('formation');
            console.log(assetInfo);
          },
        },
      ],
    },
  ];
};

export function onAssetMenu(assetInfo: MenuAssetInfo) {
  return [
    {
      label: '飞机游戏',
      submenu: [
        {
          label: '创建关卡Prefab',
          //enabled: assetInfo.isDirectory,
          click() {
            console.log('get it');
            console.log(assetInfo);
            if (assetInfo.isDirectory) {
                // 对目录下所有文件来处理

            } else {
                // 对单个文件来处理
            }
          },
        },
        {
          label: '创建子弹prefab',
          //enabled: !assetInfo.isDirectory,
          click() {
            
          },
        },
      ],
    },
  ];
};

function getAssetUuidsByPath(path: string): Promise<string[]> {
    return new Promise((resolve, reject) => {
        // @ts-ignore
        Editor.Message.request('asset-db', 'query-assets', { pattern: path }).then((res: any) => {
            const arr: any[] = Array.isArray(res) ? res : (Array.isArray(res?.[0]) ? res[0] : []);
            const assets = arr
                .filter((a: any) => a && !a.isDirectory)
                .map((a: any) => ({
                    name: String(a.name || ''),
                    path: a.path || '',
                    uuid: a.uuid || ''
                }))
                .filter(p => p.name)
                .sort((a, b) => a.name.localeCompare(b.name));
            resolve(assets.map(a => a.uuid)); // 只需要uuid即可，不需要其他信息了。
        }).catch(reject);
    });
}

function createLevelPrefab(assetInfo: IAssetInfo) {

}

function createBulletPrefab(assetInfo: IAssetInfo) {
    // 需要判断是spriteList还是单个sprite
    // 需要判断目标目录下的prefab是否已存在，如果已存在，需要提示是否覆盖已有prefab，或者跳过已有Prefab(只新建不覆盖)
    // TODO: 这个目录后续可能调整到bundle目录
    const prefabDir = 'assets/resources/game/prefabs/bullet/';
    const prefabName = assetInfo.name + '.prefab';
    const prefabPath = prefabDir + prefabName;

    // @ts-ignore
    Editor.Message.request('asset-db', 'query-uuid', prefabPath).then((uuid: string) => {
        if (uuid) {
            // 已存在，提示是否覆盖
            Editor.Dialog.info('Prefab已存在, 是否覆盖?', {
                buttons: ['覆盖', '覆盖所有', '跳过', '跳过所有'],
                default: 1,
                cancel: 1,
            }).then((action: IDialogAction) => {
                if (action.action === 'resolve') {
                    // 覆盖
                }
            });
        } else {
            // 不存在，创建
        }
    });
}