{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/wave/Wave.ts"], "names": ["_decorator", "Component", "WaveData", "eSpawnOrder", "eWaveCompletion", "GameIns", "WaveEventGroup", "WaveEventGroupContext", "ccclass", "property", "executeInEditMode", "menu", "Wave", "displayName", "editor<PERSON><PERSON><PERSON>", "type", "_isCompleted", "_waveElapsedTime", "_nextSpawnTime", "_totalWeight", "_waveCompleteParam", "_nextSpawnIndex", "_spawnQueue", "_offsetX", "_offsetY", "_eventGroups", "_eventGroupContext", "_createPlaneDelegate", "isCompleted", "onLoad", "waveData", "spawnOrder", "Random", "spawnGroup", "for<PERSON>ach", "group", "weight", "selfWeight", "reset", "length", "eventGroupData", "wave", "groupData", "push", "trigger", "x", "y", "waveCompletionParam", "eval", "waveCompletion", "SpawnCount", "i", "randomWeight", "battleManager", "random", "planeID", "tick", "dtInMiliseconds", "spawnFromQueue", "spawnFromGroup", "spawnSingleFromQueue", "spawnInterval", "index", "spawnPos", "spawnAngle", "createPlane", "planeId", "pos", "angle", "enemy", "enemyManager", "addPlane", "setPos", "initMove", "setCreatePlaneDelegate", "func"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAqDC,MAAAA,S,OAAAA,S;;AACrDC,MAAAA,Q,iBAAAA,Q;AAAUC,MAAAA,W,iBAAAA,W;AAAaC,MAAAA,e,iBAAAA,e;;AACvBC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,c,iBAAAA,c;AAAgBC,MAAAA,qB,iBAAAA,qB;;;;;;;;;OACnB;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,QAAX;AAAqBC,QAAAA,iBAArB;AAAwCC,QAAAA;AAAxC,O,GAAiDX,U;;sBAK1CY,I,WAHZJ,OAAO,CAAC,MAAD,C,UACPG,IAAI,CAAC,OAAD,C,UACJD,iBAAiB,E,UAEbD,QAAQ,CAAC;AAACI,QAAAA,WAAW,EAAE,IAAd;AAAoBC,QAAAA,UAAU,EAAE;AAAhC,OAAD,C,UAGRL,QAAQ,CAAC;AAACM,QAAAA,IAAI;AAAA;AAAA;AAAL,OAAD,C,yDAPb,MAGaH,IAHb,SAG0BX,SAH1B,CAGoC;AAAA;AAAA;;AAAA;;AAEM;AAFN;;AAOhC;AACJ;AACA;AAToC,eAUxBe,YAVwB,GAUA,KAVA;AAAA,eAaxBC,gBAbwB,GAaG,CAbH;AAAA,eAcxBC,cAdwB,GAcC,CAdD;AAAA,eAexBC,YAfwB,GAeD,CAfC;AAgBhC;AAhBgC,eAiBxBC,kBAjBwB,GAiBK,CAjBL;AAkBhC;AAlBgC,eAmBxBC,eAnBwB,GAmBE,CAnBF;AAAA,eAoBxBC,WApBwB,GAoBA,EApBA;AAqBhC;AArBgC,eAsBxBC,QAtBwB,GAsBL,CAtBK;AAAA,eAuBxBC,QAvBwB,GAuBL,CAvBK;AAwBhC;AAxBgC,eAyBxBC,YAzBwB,GAyBS,EAzBT;AAAA,eA0BxBC,kBA1BwB,GA0ByB,IA1BzB;AA6KhC;AA7KgC,eA8KxBC,oBA9KwB,GA8K2D,IA9K3D;AAAA;;AAWhC;AACsB,YAAXC,WAAW,GAAG;AAAE,iBAAO,KAAKZ,YAAZ;AAA2B;;AAgBtDa,QAAAA,MAAM,GAAG;AACL,cAAI,KAAKC,QAAL,IAAiB,KAAKA,QAAL,CAAcC,UAAd,KAA6B;AAAA;AAAA,0CAAYC,MAA9D,EAAsE;AAClE,iBAAKb,YAAL,GAAoB,CAApB,CADkE,CAElE;;AACA,iBAAKW,QAAL,CAAcG,UAAd,CAAyBC,OAAzB,CAAkCC,KAAD,IAAW;AACxC,mBAAKhB,YAAL,IAAqBgB,KAAK,CAACC,MAA3B;AACAD,cAAAA,KAAK,CAACE,UAAN,GAAmB,KAAKlB,YAAxB;AACH,aAHD;AAIH;AACJ;;AAEOmB,QAAAA,KAAK,GAAG;AACZ,eAAKtB,YAAL,GAAoB,KAApB;AACA,eAAKC,gBAAL,GAAwB,CAAxB;AACA,eAAKC,cAAL,GAAsB,CAAtB;AACA,eAAKG,eAAL,GAAuB,CAAvB;AACA,eAAKC,WAAL,CAAiBiB,MAAjB,GAA0B,CAA1B,CALY,CAMZ;;AACA,eAAKd,YAAL,CAAkBc,MAAlB,GAA2B,CAA3B;;AACA,cAAI,KAAKT,QAAL,IAAiB,KAAKA,QAAL,CAAcU,cAAnC,EAAmD;AAC/C,gBAAI,CAAC,KAAKd,kBAAV,EAA8B;AAC1B,mBAAKA,kBAAL,GAA0B;AAAA;AAAA,mEAA1B;AACA,mBAAKA,kBAAL,CAAwBe,IAAxB,GAA+B,IAA/B;AACH;;AACD,iBAAKX,QAAL,CAAcU,cAAd,CAA6BN,OAA7B,CAAsCQ,SAAD,IAAe;AAChD,oBAAMP,KAAK,GAAG;AAAA;AAAA,oDAAmB,KAAKT,kBAAxB,EAA6CgB,SAA7C,CAAd;;AACA,mBAAKjB,YAAL,CAAkBkB,IAAlB,CAAuBR,KAAvB;AACH,aAHD;AAIH;AACJ;;AAEDS,QAAAA,OAAO,CAACC,CAAD,EAAYC,CAAZ,EAAuB;AAC1B,eAAKR,KAAL;AACA,eAAKf,QAAL,GAAgBsB,CAAhB;AACA,eAAKrB,QAAL,GAAgBsB,CAAhB,CAH0B,CAK1B;;AACA,cAAI,KAAKhB,QAAT,EAAmB;AACf,iBAAKV,kBAAL,GAA0B,KAAKU,QAAL,CAAciB,mBAAd,CAAkCC,IAAlC,EAA1B;;AACA,gBAAI,KAAKlB,QAAL,CAAcmB,cAAd,KAAiC;AAAA;AAAA,oDAAgBC,UAArD,EAAiE;AAC7D,kBAAI,KAAKpB,QAAL,CAAcC,UAAd,KAA6B;AAAA;AAAA,8CAAYC,MAA7C,EAAqD;AACjD,qBAAK,IAAImB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAK/B,kBAAzB,EAA6C+B,CAAC,EAA9C,EAAkD;AAC9C,wBAAMC,YAAY,GAAG;AAAA;AAAA,0CAAQC,aAAR,CAAsBC,MAAtB,KAAiC,KAAKnC,YAA3D;;AACA,uBAAK,MAAMgB,KAAX,IAAoB,KAAKL,QAAL,CAAcG,UAAlC,EAA8C;AAC1C,wBAAImB,YAAY,IAAIjB,KAAK,CAACE,UAA1B,EAAsC;AAClC,2BAAKf,WAAL,CAAiBqB,IAAjB,CAAsBR,KAAK,CAACoB,OAA5B;;AACA;AACH;AACJ;AACJ;AACJ,eAVD,MAUO;AACH,qBAAK,IAAIJ,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAK/B,kBAAzB,EAA6C+B,CAAC,EAA9C,EAAkD;AAC9C;AACA,uBAAK7B,WAAL,CAAiBqB,IAAjB,CAAsB,KAAKb,QAAL,CAAcG,UAAd,CAAyBkB,CAAC,GAAG,KAAKrB,QAAL,CAAcG,UAAd,CAAyBM,MAAtD,EAA8DgB,OAApF;AACH;AACJ;AACJ;AACJ;AACJ,SAtF+B,CAwFhC;;;AACAC,QAAAA,IAAI,CAACC,eAAD,EAA0B;AAC1B,cAAI,KAAKzC,YAAT,EAAuB;AAEvB,eAAKC,gBAAL,IAAyBwC,eAAzB;;AACA,cAAI,KAAK3B,QAAL,CAAcmB,cAAd,KAAiC;AAAA;AAAA,kDAAgBC,UAArD,EAAiE;AAC7D;AACA,gBAAI,KAAKjC,gBAAL,IAAyB,KAAKC,cAAlC,EAAkD;AAC9C,kBAAI,CAAC,KAAKwC,cAAL,EAAL,EAA4B;AACxB,qBAAK1C,YAAL,GAAoB,IAApB;AACH;AACJ;AACJ,WAPD,MAQK;AACD;AACA,gBAAI,KAAKC,gBAAL,IAAyB,KAAKG,kBAAlC,EAAsD;AAClD,mBAAKJ,YAAL,GAAoB,IAApB;AACH,aAFD,MAEO;AACH,kBAAI,KAAKC,gBAAL,IAAyB,KAAKC,cAAlC,EAAkD;AAC9C,qBAAKyC,cAAL;AACH;AACJ;AACJ;AACJ;;AAEOD,QAAAA,cAAc,GAAY;AAC9B,cAAI,KAAKrC,eAAL,IAAwB,KAAKC,WAAL,CAAiBiB,MAA7C,EAAqD;AACjD,mBAAO,KAAP;AACH;;AAED,eAAKqB,oBAAL,CAA0B,KAAKvC,eAAL,EAA1B;AACA,eAAKH,cAAL,GAAsB,KAAKD,gBAAL,GAAwB,KAAKa,QAAL,CAAc+B,aAAd,CAA4Bb,IAA5B,EAA9C;AACA,iBAAO,IAAP;AACH;;AAEOY,QAAAA,oBAAoB,CAACE,KAAD,EAAsB;AAC9C,cAAIA,KAAK,IAAI,KAAKxC,WAAL,CAAiBiB,MAA9B,EAAsC;AAClC;AACH;;AAED,cAAIwB,QAAQ,GAAG,KAAKjC,QAAL,CAAciC,QAA7B;AACA,cAAIC,UAAU,GAAG,KAAKlC,QAAL,CAAckC,UAAd,CAAyBhB,IAAzB,EAAjB,CAN8C,CAO9C;;AAEA,eAAKiB,WAAL,CAAiB,KAAK3C,WAAL,CAAiBwC,KAAjB,CAAjB,EAA0CC,QAA1C,EAAoDC,UAApD;AACH;;AAEOL,QAAAA,cAAc,GAAS;AAC3B,eAAKzC,cAAL,GAAsB,KAAKD,gBAAL,GAAwB,KAAKa,QAAL,CAAc+B,aAAd,CAA4Bb,IAA5B,EAA9C;AAEA,cAAIe,QAAQ,GAAG,KAAKjC,QAAL,CAAciC,QAA7B;AACA,cAAIC,UAAU,GAAG,KAAKlC,QAAL,CAAckC,UAAd,CAAyBhB,IAAzB,EAAjB;;AAEA,cAAI,KAAKlB,QAAL,CAAcC,UAAd,KAA6B;AAAA;AAAA,0CAAYC,MAA7C,EAAqD;AACjD,kBAAMoB,YAAY,GAAG;AAAA;AAAA,oCAAQC,aAAR,CAAsBC,MAAtB,KAAiC,KAAKnC,YAA3D;;AACA,iBAAK,MAAMgB,KAAX,IAAoB,KAAKL,QAAL,CAAcG,UAAlC,EAA8C;AAC1C,kBAAImB,YAAY,IAAIjB,KAAK,CAACE,UAA1B,EAAsC;AAClC,qBAAK4B,WAAL,CAAiB9B,KAAK,CAACoB,OAAvB,EAAgCQ,QAAhC,EAA0CC,UAA1C;AACA;AACH;AACJ;AACJ,WARD,MAQO;AACH,iBAAKC,WAAL,CAAiB,KAAKnC,QAAL,CAAcG,UAAd,CAAyB,KAAKZ,eAAL,KAAyB,KAAKS,QAAL,CAAcG,UAAd,CAAyBM,MAA3E,EAAmFgB,OAApG,EAA6GQ,QAA7G,EAAuHC,UAAvH;AACH;AACJ;;AAEOC,QAAAA,WAAW,CAACC,OAAD,EAAkBC,GAAlB,EAA6BC,KAA7B,EAA4C;AAC3DD,UAAAA,GAAG,CAACtB,CAAJ,IAAS,KAAKtB,QAAd;AACA4C,UAAAA,GAAG,CAACrB,CAAJ,IAAS,KAAKtB,QAAd;;AAEA,cAAI,KAAKG,oBAAT,EAA+B;AAC3B,iBAAKA,oBAAL,CAA0BuC,OAA1B,EAAmCC,GAAnC,EAAwCC,KAAxC;;AACA;AACH;;AAED,cAAIC,KAAK,GAAG;AAAA;AAAA,kCAAQC,YAAR,CAAqBC,QAArB,CAA8BL,OAA9B,CAAZ;;AACA,cAAIG,KAAJ,EAAW;AACP;AACA;AACA;AACAA,YAAAA,KAAK,CAACG,MAAN,CAAaL,GAAG,CAACtB,CAAjB,EAAoBsB,GAAG,CAACrB,CAAxB;AACAuB,YAAAA,KAAK,CAACI,QAAN,CAAeL,KAAf;AACH;AACJ;;AAIMM,QAAAA,sBAAsB,CAACC,IAAD,EAA4D;AACrF,eAAKhD,oBAAL,GAA4BgD,IAA5B;AACH;;AAjL+B,O;;;;;iBAEb,E;;;;;;;iBAGW;AAAA;AAAA,qC", "sourcesContent": ["import { _decorator, CCBoolean, CCFloat, CCInteger, CCString, Component, Vec2 } from 'cc';\r\nimport { WaveData, eSpawnOrder, eWaveCompletion } from '../data/WaveData';\r\nimport { GameIns } from 'db://assets/bundles/common/script/game/GameIns';\r\nimport { WaveEventGroup, WaveEventGroupContext } from './WaveEventGroup';\r\nconst { ccclass, property, executeInEditMode, menu } = _decorator;\r\n\r\n@ccclass('Wave')\r\n@menu(\"怪物/波次\")\r\n@executeInEditMode()\r\nexport class Wave extends Component {\r\n    @property({displayName: '名称', editorOnly: true})\r\n    waveName: string = '';                // 备注(策划用)\r\n\r\n    @property({type:WaveData})\r\n    readonly waveData: WaveData = new WaveData();\r\n\r\n    /*\r\n     * 以下是新的wave逻辑, 旧的逻辑主要在WaveManager与EnemyWave\r\n     */\r\n    private _isCompleted: boolean = false;\r\n    // 当前波次是否已完成\r\n    public get isCompleted() { return this._isCompleted; }\r\n    private _waveElapsedTime: number = 0;\r\n    private _nextSpawnTime: number = 0;\r\n    private _totalWeight: number = 0;\r\n    // 这个参数可能是时间，也可能是数量，取决于waveData.waveCompletion\r\n    private _waveCompleteParam: number = 0;\r\n    // 以下两个是用在waveCompletion == SpawnCount时的队列\r\n    private _nextSpawnIndex: number = 0;\r\n    private _spawnQueue: number[] = [];\r\n    // 当前wave的偏移位置\r\n    private _offsetX: number = 0;\r\n    private _offsetY: number = 0;\r\n    // 事件组\r\n    private _eventGroups: WaveEventGroup[] = [];\r\n    private _eventGroupContext: WaveEventGroupContext|null = null;\r\n\r\n    onLoad() {\r\n        if (this.waveData && this.waveData.spawnOrder === eSpawnOrder.Random) {\r\n            this._totalWeight = 0;\r\n            // add up _totalWeight if is random\r\n            this.waveData.spawnGroup.forEach((group) => {\r\n                this._totalWeight += group.weight;\r\n                group.selfWeight = this._totalWeight;\r\n            });\r\n        }\r\n    }\r\n\r\n    private reset() {\r\n        this._isCompleted = false;\r\n        this._waveElapsedTime = 0;\r\n        this._nextSpawnTime = 0;\r\n        this._nextSpawnIndex = 0;\r\n        this._spawnQueue.length = 0;\r\n        // this._spawnQueue = this.waveData.planeList;\r\n        this._eventGroups.length = 0;\r\n        if (this.waveData && this.waveData.eventGroupData) {\r\n            if (!this._eventGroupContext) {\r\n                this._eventGroupContext = new WaveEventGroupContext();\r\n                this._eventGroupContext.wave = this;\r\n            }\r\n            this.waveData.eventGroupData.forEach((groupData) => {\r\n                const group = new WaveEventGroup(this._eventGroupContext!, groupData);\r\n                this._eventGroups.push(group);\r\n            });\r\n        }\r\n    }\r\n\r\n    trigger(x: number, y: number) {\r\n        this.reset();\r\n        this._offsetX = x;\r\n        this._offsetY = y;\r\n\r\n        // 对于固定数量的波次，可以预先生成队列，每次从里面取即可\r\n        if (this.waveData) {\r\n            this._waveCompleteParam = this.waveData.waveCompletionParam.eval();\r\n            if (this.waveData.waveCompletion === eWaveCompletion.SpawnCount) {\r\n                if (this.waveData.spawnOrder === eSpawnOrder.Random) {\r\n                    for (let i = 0; i < this._waveCompleteParam; i++) {\r\n                        const randomWeight = GameIns.battleManager.random() * this._totalWeight;\r\n                        for (const group of this.waveData.spawnGroup) {\r\n                            if (randomWeight <= group.selfWeight) {\r\n                                this._spawnQueue.push(group.planeID);\r\n                                break;\r\n                            }\r\n                        }\r\n                    }\r\n                } else {\r\n                    for (let i = 0; i < this._waveCompleteParam; i++) {\r\n                        // 通过取余实现循环\r\n                        this._spawnQueue.push(this.waveData.spawnGroup[i % this.waveData.spawnGroup.length].planeID);\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    // tick wave\r\n    tick(dtInMiliseconds: number) {\r\n        if (this._isCompleted) return;\r\n\r\n        this._waveElapsedTime += dtInMiliseconds;\r\n        if (this.waveData.waveCompletion === eWaveCompletion.SpawnCount) {\r\n            // 产出固定数量的波次\r\n            if (this._waveElapsedTime >= this._nextSpawnTime) {\r\n                if (!this.spawnFromQueue()) {\r\n                    this._isCompleted = true;\r\n                }\r\n            }\r\n        }\r\n        else {\r\n            // 完全根据时间的波次\r\n            if (this._waveElapsedTime >= this._waveCompleteParam) {\r\n                this._isCompleted = true;\r\n            } else {\r\n                if (this._waveElapsedTime >= this._nextSpawnTime) {\r\n                    this.spawnFromGroup();\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    private spawnFromQueue(): boolean {        \r\n        if (this._nextSpawnIndex >= this._spawnQueue.length) {\r\n            return false;\r\n        }\r\n\r\n        this.spawnSingleFromQueue(this._nextSpawnIndex++);\r\n        this._nextSpawnTime = this._waveElapsedTime + this.waveData.spawnInterval.eval();\r\n        return true;\r\n    }\r\n\r\n    private spawnSingleFromQueue(index: number): void {\r\n        if (index >= this._spawnQueue.length) {\r\n            return;\r\n        }\r\n\r\n        let spawnPos = this.waveData.spawnPos;\r\n        let spawnAngle = this.waveData.spawnAngle.eval();\r\n        // let spawnSpeed = this.waveData.spawnSpeed.eval();\r\n\r\n        this.createPlane(this._spawnQueue[index], spawnPos, spawnAngle);\r\n    }\r\n\r\n    private spawnFromGroup(): void {\r\n        this._nextSpawnTime = this._waveElapsedTime + this.waveData.spawnInterval.eval();\r\n\r\n        let spawnPos = this.waveData.spawnPos;\r\n        let spawnAngle = this.waveData.spawnAngle.eval();\r\n\r\n        if (this.waveData.spawnOrder === eSpawnOrder.Random) {\r\n            const randomWeight = GameIns.battleManager.random() * this._totalWeight;\r\n            for (const group of this.waveData.spawnGroup) {\r\n                if (randomWeight <= group.selfWeight) {\r\n                    this.createPlane(group.planeID, spawnPos, spawnAngle);\r\n                    break;\r\n                }\r\n            }\r\n        } else {\r\n            this.createPlane(this.waveData.spawnGroup[this._nextSpawnIndex++ % this.waveData.spawnGroup.length].planeID, spawnPos, spawnAngle);\r\n        }\r\n    }\r\n\r\n    private createPlane(planeId: number, pos: Vec2, angle: number) {\r\n        pos.x += this._offsetX;\r\n        pos.y += this._offsetY;\r\n        \r\n        if (this._createPlaneDelegate) {\r\n            this._createPlaneDelegate(planeId, pos, angle);\r\n            return;\r\n        }\r\n\r\n        let enemy = GameIns.enemyManager.addPlane(planeId);\r\n        if (enemy) {\r\n            // enemy.initTrack(this.waveData.trackGroups, this.waveData.liveParam, spawnPos.x, spawnPos.y);\r\n            // enemy.setStandByTime(0);\r\n            // console.log(\"createPlane\", planeId, pos, angle, speed);\r\n            enemy.setPos(pos.x, pos.y);\r\n            enemy.initMove(angle);\r\n        }\r\n    }\r\n\r\n    // 以下几个函数是为了给编辑器预览用\r\n    private _createPlaneDelegate: ((planeId: number, pos: Vec2, angle: number) => void)|null = null;\r\n    public setCreatePlaneDelegate(func: (planeId: number, pos: Vec2, angle: number) => void) {\r\n        this._createPlaneDelegate = func;\r\n    }\r\n}"]}