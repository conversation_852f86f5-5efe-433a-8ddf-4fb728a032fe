System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, eE<PERSON>ter<PERSON>tatus, Emitter, Entity, _decorator, _dec, _dec2, _dec3, _dec4, _dec5, _dec6, _class, _class2, _descriptor, _descriptor2, _descriptor3, _descriptor4, _descriptor5, _crd, ccclass, property, eWeaponUseCond, eWeaponState, Weapon;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfeEmitterStatus(extras) {
    _reporterNs.report("eEmitterStatus", "db://assets/bundles/common/script/game/bullet/Emitter", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEmitter(extras) {
    _reporterNs.report("Emitter", "db://assets/bundles/common/script/game/bullet/Emitter", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEntity(extras) {
    _reporterNs.report("Entity", "db://assets/bundles/common/script/game/ui/base/Entity", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
    }, function (_unresolved_2) {
      eEmitterStatus = _unresolved_2.eEmitterStatus;
      Emitter = _unresolved_2.Emitter;
    }, function (_unresolved_3) {
      Entity = _unresolved_3.default;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "1fdc1ZnRu5MFLj4D97LAXCR", "Weapon", undefined);

      __checkObsolete__(['_decorator', 'Component']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("eWeaponUseCond", eWeaponUseCond = {
        Immediate: 0,
        DelayTime: 1,
        WeaponDestroyed: 2
      });

      _export("eWeaponState", eWeaponState = {
        Aiming: 0,
        Emitting: 1,
        None: 2
      });
      /**
       * 武器后续可能也需要继承自PlaneBase, 因为武器可能也需要血量，扣血等逻辑。
       */


      _export("Weapon", Weapon = (_dec = ccclass('Weapon'), _dec2 = property({
        displayName: "转向速度"
      }), _dec3 = property({
        displayName: "锁定目标时间"
      }), _dec4 = property({
        displayName: "启用条件"
      }), _dec5 = property({
        type: [Weapon],
        displayName: "目标武器",

        visible() {
          // @ts-ignore
          return this.useCondition == eWeaponUseCond.WeaponDestroyed;
        }

      }), _dec6 = property({
        type: _crd && Emitter === void 0 ? (_reportPossibleCrUseOfEmitter({
          error: Error()
        }), Emitter) : Emitter,
        displayName: "发射器"
      }), _dec(_class = (_class2 = class Weapon extends (_crd && Entity === void 0 ? (_reportPossibleCrUseOfEntity({
        error: Error()
      }), Entity) : Entity) {
        constructor(...args) {
          super(...args);

          _initializerDefineProperty(this, "turnSpeed", _descriptor, this);

          // 转向速度（仅用在追踪目标时）
          _initializerDefineProperty(this, "aimingTime", _descriptor2, this);

          // 锁定目标时间，单位ms
          _initializerDefineProperty(this, "useCondition", _descriptor3, this);

          _initializerDefineProperty(this, "targetWeapons", _descriptor4, this);

          _initializerDefineProperty(this, "emitter", _descriptor5, this);

          // 武器发射器
          this.m_state = eWeaponState.None;
          this.m_stateElapsedTime = 0;
          // 当前状态持续时间
          this.m_target = null;
          // 武器目标
          this.m_owner = null;
        }

        // 武器归属
        init() {
          this.m_state = eWeaponState.None;

          if (this.emitter) {
            this.emitter.changeStatus((_crd && eEmitterStatus === void 0 ? (_reportPossibleCrUseOfeEmitterStatus({
              error: Error()
            }), eEmitterStatus) : eEmitterStatus).None);
          }
        }

        get state() {
          return this.m_state;
        }

        set state(value) {
          if (this.m_state === value) {
            return;
          }

          this.m_state = value;
          this.m_stateElapsedTime = 0;
        }

        setOwner(owner) {
          this.m_owner = owner;
          return this;
        }

        setTarget(target) {
          this.m_target = target;

          if (this.useCondition === eWeaponUseCond.Immediate) {
            this.activate();
          }

          return this;
        }

        tick(dt) {
          this.m_stateElapsedTime += dt;

          switch (this.m_state) {
            case eWeaponState.Aiming:
              this.tickAiming(dt);
              break;

            case eWeaponState.Emitting:
              this.tickEmitting(dt);
              break;

            case eWeaponState.None:
              break;
          }
        } // 激活武器


        activate() {
          this.state = this.aimingTime > 0 ? eWeaponState.Aiming : eWeaponState.Emitting;
        }

        tickAiming(dt) {
          if (this.m_stateElapsedTime >= this.aimingTime) {
            this.state = eWeaponState.Emitting;
          }

          this.turnToTarget(dt);
        }

        tickEmitting(dt) {
          if (this.emitter && this.emitter.status !== (_crd && eEmitterStatus === void 0 ? (_reportPossibleCrUseOfeEmitterStatus({
            error: Error()
          }), eEmitterStatus) : eEmitterStatus).Emitting) {
            this.emitter.changeStatus((_crd && eEmitterStatus === void 0 ? (_reportPossibleCrUseOfeEmitterStatus({
              error: Error()
            }), eEmitterStatus) : eEmitterStatus).Emitting);
          }
        }

        tickNone(dt) {
          if (this.useCondition === eWeaponUseCond.WeaponDestroyed) {
            for (let weapon of this.targetWeapons) {
              if (!weapon.isDead) {
                return;
              }
            }

            this.activate();
          }
        }

        turnToTarget(dt) {
          if (!this.m_target || !this.m_owner) {
            return;
          }

          const ownerNode = this.m_owner.node;
          const targetNode = this.m_target.node;
          const angle = ownerNode.angle;
          const targetAngle = Math.atan2(targetNode.worldPosition.y - ownerNode.worldPosition.y, targetNode.worldPosition.x - ownerNode.worldPosition.x) * 180 / Math.PI - 90;
          let deltaAngle = targetAngle - angle;

          if (deltaAngle > 180) {
            deltaAngle -= 360;
          } else if (deltaAngle < -180) {
            deltaAngle += 360;
          }

          const maxDelta = this.turnSpeed * dt / 1000;

          if (Math.abs(deltaAngle) <= maxDelta) {
            ownerNode.angle = targetAngle;
          } else {
            ownerNode.angle += Math.sign(deltaAngle) * maxDelta;
          }
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "turnSpeed", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 60;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "aimingTime", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 2000;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "useCondition", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return eWeaponUseCond.Immediate;
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class2.prototype, "targetWeapons", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return [];
        }
      }), _descriptor5 = _applyDecoratedDescriptor(_class2.prototype, "emitter", [_dec6], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=dfce8e34e31d3004f1d8615ca0a443cca4efbfba.js.map